import React, { useState, useEffect } from 'react';
import { Form, Button, Card, Alert } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { getSupabase } from '../supabaseClient';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaEye, FaEyeSlash } from 'react-icons/fa';

const ResetPasswordPage = () => {
  const { t } = useTranslation();
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [validResetRequest, setValidResetRequest] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const supabase = getSupabase();
    if (!supabase) return;

    // 检查URL参数中是否有错误信息或认证token
    const checkUrlParams = async () => {
      const fullUrl = window.location.href;

      // 处理URL中的参数，可能在不同位置
      let urlParams = new URLSearchParams();

      // 方法1: 检查URL中是否有多个#符号的情况
      if (fullUrl.includes('#access_token=')) {
        // 找到最后一个#access_token=的位置
        const lastHashIndex = fullUrl.lastIndexOf('#access_token=');
        const paramString = fullUrl.substring(lastHashIndex + 1); // 去掉#
        urlParams = new URLSearchParams(paramString);
      }
      // 方法2: 检查URL中是否有&access_token=的情况
      else if (fullUrl.includes('&access_token=')) {
        const tokenIndex = fullUrl.indexOf('access_token=');
        const paramString = fullUrl.substring(tokenIndex);
        urlParams = new URLSearchParams(paramString);
      }
      // 方法3: 检查标准的hash参数
      else if (window.location.hash) {
        const hash = window.location.hash;
        if (hash.includes('?')) {
          // 格式: #/reset-password?access_token=...
          const queryString = hash.split('?')[1];
          urlParams = new URLSearchParams(queryString);
        } else if (hash.includes('access_token=')) {
          // 格式: #access_token=...
          const queryString = hash.substring(1);
          urlParams = new URLSearchParams(queryString);
        }
      }

      // 检查错误参数
      if (urlParams.get('error')) {
        const errorCode = urlParams.get('error_code');
        const errorDescription = urlParams.get('error_description');

        if (errorCode === 'otp_expired') {
          setError(t('reset_link_expired'));
        } else {
          setError(errorDescription || t('reset_link_invalid'));
        }
        return;
      }

      // 检查是否有access_token，表示重置链接有效
      const accessToken = urlParams.get('access_token');
      const refreshToken = urlParams.get('refresh_token');
      const type = urlParams.get('type');

      if (accessToken && type === 'recovery') {
        setValidResetRequest(true);

        // 设置session
        if (refreshToken) {
          try {
            const { error } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken
            });

            if (error) {
              console.error('Error setting session:', error);
              setError(t('reset_link_invalid'));
            } else {
              console.log('Session set successfully');
              // 清理URL中的token参数
              window.history.replaceState({}, document.title, window.location.pathname + '#/reset-password');
            }
          } catch (err) {
            console.error('Error setting session:', err);
            setError(t('reset_link_invalid'));
          }
        }
      } else if (accessToken) {
        // 有access_token但没有type=recovery，可能是其他类型的token
        setError(t('reset_link_invalid'));
      } else {
        // 没有access_token，检查当前session
        const { data: { session } } = await supabase.auth.getSession();
        if (session) {
          setValidResetRequest(true);
        } else {
          setError(t('reset_link_invalid'));
        }
      }
    };

    checkUrlParams();

    // 监听认证状态变化
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state change:', event, session);

      if (event === 'PASSWORD_RECOVERY') {
        // 用户通过密码重置链接访问，可以更新密码
        console.log('Password recovery event detected');
        setValidResetRequest(true);
      } else if (event === 'SIGNED_IN' && session) {
        // 密码更新成功后会触发SIGNED_IN事件
        console.log('User signed in after password reset');
      } else if (event === 'TOKEN_REFRESHED' && session) {
        // Token刷新事件
        console.log('Token refreshed');
        setValidResetRequest(true);
      }
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, [t]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    // 验证密码
    if (newPassword.length < 6) {
      setError(t('password_too_short'));
      setLoading(false);
      return;
    }

    if (newPassword !== confirmPassword) {
      setError(t('passwords_do_not_match'));
      setLoading(false);
      return;
    }

    try {
      const supabase = getSupabase();
      if (!supabase) {
        throw new Error(t('backend_connection_failed'));
      }

      // 更新用户密码
      const { error: updateError } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (updateError) throw updateError;

      setSuccess(true);
      
      // 3秒后跳转到登录页面
      setTimeout(() => {
        navigate('/login', { replace: true });
      }, 3000);

    } catch (err) {
      console.error('Reset Password Error:', err);
      setError(err.message || t('password_update_failed'));
    }

    setLoading(false);
  };

  if (success) {
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <Card style={{ width: '400px' }}>
          <Card.Body>
            <Card.Title className="text-center mb-4">{t('reset_password')}</Card.Title>
            <Alert variant="success" className="text-center">
              {t('password_updated_successfully')}
            </Alert>
            <p className="text-center text-muted">
              {t('redirecting_to_login')}
            </p>
          </Card.Body>
        </Card>
      </div>
    );
  }

  // 如果链接无效或已过期，显示错误信息
  if (error && !validResetRequest) {
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <Card style={{ width: '400px' }}>
          <Card.Body>
            <Card.Title className="text-center mb-4">{t('reset_password')}</Card.Title>
            <Alert variant="danger" className="text-center">
              {error}
            </Alert>
            <div className="text-center mt-3">
              <Button
                variant="primary"
                onClick={() => navigate('/login')}
              >
                {t('back_to_login')}
              </Button>
            </div>
          </Card.Body>
        </Card>
      </div>
    );
  }

  return (
    <div className="d-flex justify-content-center align-items-center vh-100">
      <Card style={{ width: '400px' }}>
        <Card.Body>
          <Card.Title className="text-center mb-4">{t('reset_password')}</Card.Title>
          {error && !validResetRequest && <Alert variant="danger">{error}</Alert>}
          {validResetRequest ? (
            <Form onSubmit={handleSubmit}>
              <Form.Group className="mb-3">
                <Form.Label><span className="text-danger">*</span>{t('new_password')}</Form.Label>
                <div style={{ position: 'relative' }}>
                  <Form.Control
                    type={showNewPassword ? "text" : "password"}
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    placeholder={t('enter_new_password')}
                    required
                    style={{ paddingRight: '40px' }}
                  />
                  <span
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    style={{
                      position: 'absolute',
                      right: '12px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      cursor: 'pointer',
                      color: '#6c757d',
                      fontSize: '16px'
                    }}
                  >
                    {showNewPassword ? <FaEyeSlash /> : <FaEye />}
                  </span>
                </div>
                <Form.Text className="text-muted">
                  {t('password_requirements')}
                </Form.Text>
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label><span className="text-danger">*</span>{t('confirm_new_password')}</Form.Label>
                <div style={{ position: 'relative' }}>
                  <Form.Control
                    type={showConfirmPassword ? "text" : "password"}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder={t('confirm_new_password')}
                    required
                    style={{ paddingRight: '40px' }}
                  />
                  <span
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    style={{
                      position: 'absolute',
                      right: '12px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      cursor: 'pointer',
                      color: '#6c757d',
                      fontSize: '16px'
                    }}
                  >
                    {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                  </span>
                </div>
              </Form.Group>
              <Button type="submit" className="w-100" disabled={loading}>
                {loading ? t('updating') : t('update_password')}
              </Button>
              {error && <Alert variant="danger" className="mt-3">{error}</Alert>}
            </Form>
          ) : (
            <div className="text-center">
              <Alert variant="warning">
                {t('reset_link_invalid')}
              </Alert>
            </div>
          )}

          <div className="text-center mt-3">
            <Button
              variant="link"
              className="p-0 text-decoration-none"
              onClick={() => navigate('/login')}
            >
              {t('back_to_login')}
            </Button>
          </div>
        </Card.Body>
      </Card>
    </div>
  );
};

export default ResetPasswordPage;
