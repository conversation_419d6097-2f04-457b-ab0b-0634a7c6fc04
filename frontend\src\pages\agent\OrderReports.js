import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Form, InputGroup, Button, Pagination } from 'react-bootstrap';
import { FaDownload } from 'react-icons/fa';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import StatusBadge from '../../components/StatusBadge';

const OrderReports = () => {
    const { t } = useTranslation();
    const [orders, setOrders] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [filteredOrders, setFilteredOrders] = useState([]);

    // Pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [ordersPerPage] = useState(10);
    const [paginatedOrders, setPaginatedOrders] = useState([]);

    useEffect(() => {
        const fetchOrders = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // First get the agent profile for the current user
            const { data: agentProfile, error: agentError } = await supabase
                .from('agent_profiles')
                .select('user_id')
                .eq('user_id', user.id)
                .single();

            if (agentError || !agentProfile) {
                console.error('Error fetching agent profile:', agentError);
                setLoading(false);
                return;
            }

            // Fetch orders for this specific agent with related information
            const { data, error } = await supabase
                .from('orders')
                .select(`
                    id,
                    customer_id,
                    shares,
                    proof_image_url,
                    storage_cost,
                    pledge_cost,
                    total_rate,
                    tech_fee_pct,
                    sales_fee_pct,
                    ops_fee_pct,
                    start_at,
                    end_at,
                    review_status,
                    created_at,
                    updated_at,
                    products (
                        name,
                        category,
                        price
                    ),
                    customer_profiles (
                        real_name,
                        users (
                            email
                        )
                    )
                `)
                .eq('agent_id', agentProfile.user_id)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching orders:', error);
            } else {
                setOrders(data);
            }
            setLoading(false);
        };

        fetchOrders();
    }, []);

    // Filter orders based on search criteria
    useEffect(() => {
        let filtered = orders;

        // Search by customer email or real_name
        if (searchTerm) {
            filtered = filtered.filter(order =>
                order.customer_profiles?.users?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                order.customer_profiles?.real_name?.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Filter by date range
        if (startDate) {
            filtered = filtered.filter(order =>
                new Date(order.created_at) >= new Date(startDate)
            );
        }
        if (endDate) {
            filtered = filtered.filter(order =>
                new Date(order.created_at) <= new Date(endDate)
            );
        }

        setFilteredOrders(filtered);
        setCurrentPage(1); // Reset to first page when filters change
    }, [orders, searchTerm, startDate, endDate]);

    // Paginate filtered orders
    useEffect(() => {
        const indexOfLastOrder = currentPage * ordersPerPage;
        const indexOfFirstOrder = indexOfLastOrder - ordersPerPage;
        const currentOrders = filteredOrders.slice(indexOfFirstOrder, indexOfLastOrder);
        setPaginatedOrders(currentOrders);
    }, [filteredOrders, currentPage, ordersPerPage]);

    // Pagination handlers
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const totalPages = Math.ceil(filteredOrders.length / ordersPerPage);

    // Export filtered orders to CSV
    const exportToCSV = () => {
        if (filteredOrders.length === 0) {
            alert(t('no_data_to_export'));
            return;
        }

        // Define CSV headers
        const headers = [
            t('order_id'),
            t('product_name'),
            t('customer'),
            t('shares'),
            t('storage_cost'),
            t('total_rate'),
            t('sales_commission'),
            t('start_date'),
            t('end_date'),
            t('review_status'),
            t('created_at')
        ];

        // Convert data to CSV format
        const csvData = filteredOrders.map(order => [
            order.customer_id || order.id,
            order.products?.name || '-',
            `${order.customer_profiles?.real_name || '-'} (${order.customer_profiles?.users?.email || '-'})`,
            order.shares?.toFixed(2) || '0.00',
            order.storage_cost?.toFixed(6) || '0.000000',
            `${order.total_rate?.toFixed(4) || '0.0000'}%`,
            order.sales_fee_pct ? `${order.sales_fee_pct.toFixed(2)}%` : '-',
            order.start_at || '-',
            order.end_at || '-',
            order.review_status || '-',
            new Date(order.created_at).toLocaleString()
        ]);

        // Combine headers and data
        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Add UTF-8 BOM to ensure proper encoding for Japanese characters
        const BOM = '\uFEFF';
        const csvWithBOM = BOM + csvContent;

        // Create and download CSV file with proper UTF-8 encoding
        const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `agent_order_reports_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    if (loading) {
        return <div>{t('loading_order_reports')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('order_list')}</h2>

            {/* Top Operation Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('export_data')}</Form.Label>
                                        <div>
                                            <Button
                                                variant="success"
                                                onClick={exportToCSV}
                                                className="mb-2"
                                                disabled={filteredOrders.length === 0}
                                            >
                                                <FaDownload className="me-1" />
                                                {t('export_all')}
                                            </Button>
                                        </div>
                                    </Form.Group>
                                </Col>
                                <Col md={4}>
                                    <Form.Group>
                                        <Form.Label>{t('search_customer')}</Form.Label>
                                        <InputGroup>
                                            <Form.Control
                                                type="text"
                                                placeholder={t('search_by_email_or_name')}
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                            />
                                        </InputGroup>
                                    </Form.Group>
                                </Col>
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('start_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('end_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('order_id')}</th>
                                        <th>{t('product_name')}</th>
                                        <th>{t('customer')}</th>
                                        <th>{t('shares')}</th>
                                        <th>{t('storage_cost')}</th>
                                        <th>{t('total_rate')}</th>
                                        <th>{t('sales_commission')}</th>
                                        <th>{t('start_date')}</th>
                                        <th>{t('end_date')}</th>
                                        <th>{t('review_status')}</th>
                                        <th>{t('created_at')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedOrders.length === 0 ? (
                                        <tr>
                                            <td colSpan="11" className="text-center">{t('no_orders_available')}</td>
                                        </tr>
                                    ) : (
                                        paginatedOrders.map(order => (
                                            <tr key={order.id}>
                                                <td>{order.customer_id || order.id}</td>
                                                <td>{order.products?.name || '-'}</td>
                                                <td>
                                                    <div>
                                                        <div>{order.customer_profiles?.real_name || '-'}</div>
                                                        <small className="text-muted">
                                                            {order.customer_profiles?.users?.email || '-'}
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>{order.shares?.toFixed(2) || '0.00'}</td>
                                                <td>{order.storage_cost?.toFixed(6) || '0.000000'}</td>
                                                <td>{order.total_rate?.toFixed(4) || '0.0000'}%</td>
                                                <td>
                                                    {order.sales_fee_pct ?
                                                        `${order.sales_fee_pct.toFixed(2)}%` :
                                                        '-'
                                                    }
                                                </td>
                                                <td>{order.start_at || '-'}</td>
                                                <td>{order.end_at || '-'}</td>
                                                <td><StatusBadge status={order.review_status} type="review" /></td>
                                                <td>{new Date(order.created_at).toLocaleString()}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="d-flex justify-content-center">
                                    <Pagination>
                                        <Pagination.First
                                            onClick={() => handlePageChange(1)}
                                            disabled={currentPage === 1}
                                        />
                                        <Pagination.Prev
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                        />

                                        {/* Show page numbers */}
                                        {[...Array(totalPages)].map((_, index) => {
                                            const pageNumber = index + 1;
                                            // Show first page, last page, current page, and pages around current page
                                            if (
                                                pageNumber === 1 ||
                                                pageNumber === totalPages ||
                                                (pageNumber >= currentPage - 2 && pageNumber <= currentPage + 2)
                                            ) {
                                                return (
                                                    <Pagination.Item
                                                        key={pageNumber}
                                                        active={pageNumber === currentPage}
                                                        onClick={() => handlePageChange(pageNumber)}
                                                    >
                                                        {pageNumber}
                                                    </Pagination.Item>
                                                );
                                            } else if (
                                                pageNumber === currentPage - 3 ||
                                                pageNumber === currentPage + 3
                                            ) {
                                                return <Pagination.Ellipsis key={pageNumber} />;
                                            }
                                            return null;
                                        })}

                                        <Pagination.Next
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                        />
                                        <Pagination.Last
                                            onClick={() => handlePageChange(totalPages)}
                                            disabled={currentPage === totalPages}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default OrderReports;