<?php
// Exit if accessed directly.
if (!defined('ABSPATH')) {
    exit;
}

// This file will now be much simpler.
class FIL_Platform_API {

    public function __construct() {
        add_action('rest_api_init', [$this, 'register_routes']);
    }

    public function register_routes() {
        
        register_rest_route('fil-platform/v1', '/config', [
            [
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$this, 'get_supabase_config'],
                'permission_callback' => '__return_true', // Publicly accessible
            ],
        ]);

        // Add manual scraper trigger endpoint
        register_rest_route('fil-platform/v1', '/scrape-filfox', [
            [
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => [$this, 'manual_scrape_filfox'],
                'permission_callback' => [$this, 'check_admin_permission'],
            ],
        ]);

        // Add scraper status endpoint
        register_rest_route('fil-platform/v1', '/scraper-status', [
            [
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$this, 'get_scraper_status'],
                'permission_callback' => [$this, 'check_admin_permission'],
            ],
        ]);

        // Add real-time filfox data endpoint
        register_rest_route('fil-platform/v1', '/filfox-realtime', [
            [
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$this, 'get_filfox_realtime_data'],
                'permission_callback' => '__return_true', // Allow public access for now
            ],
        ]);

        // Add test endpoint
        register_rest_route('fil-platform/v1', '/test', [
            [
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$this, 'test_endpoint'],
                'permission_callback' => '__return_true',
            ],
        ]);

        // Add simple test endpoint for debugging
        register_rest_route('fil-platform/v1', '/debug', [
            [
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$this, 'debug_endpoint'],
                'permission_callback' => '__return_true',
            ],
        ]);

        // Add KYC file upload endpoint
        register_rest_route('fil-platform/v1', '/upload-kyc-image', [
            [
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => [$this, 'upload_kyc_image'],
                'permission_callback' => '__return_true',
            ],
        ]);

        // Add KYC submission endpoint
        register_rest_route('fil-platform/v1', '/submit-kyc', [
            [
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => [$this, 'submit_kyc'],
                'permission_callback' => '__return_true',
            ],
        ]);

        // Add general image upload endpoint
        register_rest_route('fil-platform/v1', '/upload-image', [
            [
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => [$this, 'upload_image'],
                'permission_callback' => '__return_true',
            ],
        ]);

        // Add create member endpoint
        register_rest_route('fil-platform/v1', '/create-member', [
            [
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => [$this, 'create_member'],
                'permission_callback' => '__return_true',
            ],
        ]);

        // Add customer registration endpoint (public)
        register_rest_route('fil-platform/v1', '/register-customer', [
            [
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => [$this, 'register_customer'],
                'permission_callback' => '__return_true', // Public endpoint
            ],
        ]);

        // Add test auth endpoint for debugging
        register_rest_route('fil-platform/v1', '/test-auth', [
            [
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => [$this, 'test_auth'],
                'permission_callback' => '__return_true',
            ],
        ]);

        // Add withdrawal processing endpoint
        register_rest_route('fil-platform/v1', '/process-withdrawal', [
            [
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => [$this, 'process_withdrawal'],
                'permission_callback' => '__return_true',
            ],
        ]);

        // Add change member password endpoint
        register_rest_route('fil-platform/v1', '/change-member-password', [
            [
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => [$this, 'change_member_password'],
                'permission_callback' => [$this, 'check_user_permission'],
            ],
        ]);

        error_log('FIL Platform: API routes registered successfully');
    }

    public function get_supabase_config() {
        // IMPORTANT: These values should be stored securely,
        // for example, in wp-config.php, not directly in the code.
        // For this example, I will define them here.
        // The user will need to replace these with their actual Supabase credentials.
        $supabase_url = get_option('fil_platform_supabase_url', 'YOUR_SUPABASE_URL');
        $supabase_anon_key = get_option('fil_platform_supabase_anon_key', 'YOUR_SUPABASE_ANON_KEY');

        if ($supabase_url === 'YOUR_SUPABASE_URL' || $supabase_anon_key === 'YOUR_SUPABASE_ANON_KEY') {
            return new WP_Error(
                'supabase_not_configured',
                'Supabase URL and Key are not configured in WordPress settings.',
                ['status' => 500]
            );
        }

        return new WP_REST_Response([
            'url' => $supabase_url,
            'anonKey' => $supabase_anon_key,
        ], 200);
    }

    /**
     * Check admin permission
     */
    public function check_admin_permission() {
        return current_user_can('manage_options');
    }

    /**
     * Check user permission (any logged-in user)
     */
    public function check_user_permission() {
        return is_user_logged_in();
    }

    /**
     * Test endpoint
     */
    public function test_endpoint($request) {
        error_log('FIL Platform: Test endpoint called');
        return new WP_REST_Response([
            'success' => true,
            'message' => 'Test endpoint is working',
            'timestamp' => current_time('mysql')
        ], 200);
    }

    /**
     * Manual scrape filfox data
     */
    public function manual_scrape_filfox($request) {
        error_log('FIL Platform: Manual scrape endpoint called');

        try {
            // Check if scraper class exists
            if (!class_exists('FIL_Platform_Filfox_Scraper')) {
                error_log('FIL Platform: FIL_Platform_Filfox_Scraper class not found');
                return new WP_REST_Response([
                    'success' => false,
                    'message' => 'Scraper class not found',
                    'error' => 'FIL_Platform_Filfox_Scraper class not available'
                ], 500);
            }

            // Create scraper instance
            $scraper = new FIL_Platform_Filfox_Scraper();
            
            // Run the scraper
            $result = $this->scrape_filfox_realtime();


            if ($result['success']) {
                return new WP_REST_Response([
                    'success' => true,
                    'message' => 'Filfox data scraped successfully',
                    'data' => $result['data']
                ], 200);
            } else {
                return new WP_REST_Response([
                    'success' => false,
                    'message' => 'Failed to scrape filfox data',
                    'error' => $result['error']
                ], 500);
            }

        } catch (Exception $e) {
            error_log('FIL Platform: Exception in manual scrape: ' . $e->getMessage());
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Exception occurred',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get scraper status
     */
    public function get_scraper_status($request) {
        $next_scheduled = wp_next_scheduled('fil_platform_filfox_scraper');
        $last_run = get_option('fil_platform_last_scrape_run', 'Never');
        $last_success = get_option('fil_platform_last_scrape_success', 'Never');

        return new WP_REST_Response([
            'next_scheduled' => $next_scheduled ? date('Y-m-d H:i:s', $next_scheduled) . ' UTC' : 'Not scheduled',
            'next_scheduled_jst' => $next_scheduled ? date('Y-m-d H:i:s', $next_scheduled + 9 * 3600) . ' JST' : 'Not scheduled',
            'last_run' => $last_run,
            'last_success' => $last_success,
            'cron_enabled' => !defined('DISABLE_WP_CRON') || !DISABLE_WP_CRON
        ], 200);
    }

    /**
     * Get real-time filfox data
     */
    public function get_filfox_realtime_data($request) {

        try {
            // Scrape data from filfox.info in real-time
            $result = $this->scrape_filfox_realtime();

            if ($result['success']) {
                return new WP_REST_Response([
                    'success' => true,
                    'data' => $result['data'],
                    'timestamp' => current_time('mysql')
                ], 200);
            } else {
                return new WP_REST_Response([
                    'success' => false,
                    'message' => 'Failed to fetch real-time data',
                    'error' => $result['error']
                ], 500);
            }

        } catch (Exception $e) {
            error_log('FIL Platform: Exception in real-time API: ' . $e->getMessage());
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Exception occurred',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Helper method to call scraper functionality
     */
    private function call_scraper_method() {
        // Directly implement scraping logic here for API access
        update_option('fil_platform_last_scrape_run', current_time('mysql'));

        try {
            $response = wp_remote_get('https://filfox.info/en', [
                'timeout' => 30,
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'headers' => [
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language' => 'en-US,en;q=0.5',
                    'Accept-Encoding' => 'gzip, deflate',
                    'Connection' => 'keep-alive',
                ]
            ]);

            if (is_wp_error($response)) {
                throw new Exception('HTTP request failed: ' . $response->get_error_message());
            }

            $body = wp_remote_retrieve_body($response);
            $status_code = wp_remote_retrieve_response_code($response);

            if ($status_code !== 200) {
                throw new Exception('HTTP request returned status code: ' . $status_code);
            }

            if (empty($body)) {
                throw new Exception('Empty response body');
            }

            // Parse the HTML to extract data
            $data = $this->parse_filfox_html($body);

            if (!$data['mining_reward']) {
                throw new Exception('No valid data found in the response');
            }

            update_option('fil_platform_last_scrape_success', current_time('mysql'));

            return [
                'success' => true,
                'data' => $data
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Parse HTML content (simplified version for API)
     */
    private function parse_filfox_html($html) {
        $mining_reward = null;

        // Remove script and style tags
        $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);
        $html = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $html);

        // Try to find mining reward
        if (preg_match('/24h\s+Average\s+Mining\s+Reward[^0-9]*([0-9]+\.?[0-9]*)\s*FIL\/TiB/i', $html, $matches)) {
            $reward = (float) $matches[1];
            if ($reward > 0 && $reward < 10) {
                $mining_reward = $reward;
            }
        }

        // Alternative pattern for mining reward
        if (!$mining_reward && preg_match('/([0-9]*\.?[0-9]+)\s*FIL\/TiB/i', $html, $matches)) {
            $reward = (float) $matches[1];
            if ($reward > 0 && $reward < 10) {
                $mining_reward = $reward;
            }
        }

        return [
            'mining_reward' => $mining_reward ?: 0.0,
            'scraped_at' => current_time('mysql')
        ];
    }

    /**
     * Scrape filfox data in real-time with all network statistics
     */
    private function scrape_filfox_realtime() {
        try {
            // Use WordPress HTTP API to fetch the page
            $response = wp_remote_get('https://filfox.info/en', [
                'timeout' => 30,
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'headers' => [
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language' => 'en-US,en;q=0.5',
                    'Accept-Encoding' => 'gzip, deflate',
                    'Connection' => 'keep-alive',
                ]
            ]);

            if (is_wp_error($response)) {
                throw new Exception('HTTP request failed: ' . $response->get_error_message());
            }

            $body = wp_remote_retrieve_body($response);
            $status_code = wp_remote_retrieve_response_code($response);

            if ($status_code !== 200) {
                throw new Exception('HTTP request returned status code: ' . $status_code);
            }

            if (empty($body)) {
                throw new Exception('Empty response body');
            }

            // Parse the HTML to extract all network data
            $data = $this->parse_filfox_realtime_html($body);

            // Validate that we have at least some essential data
            if (!$data['mining_reward'] && !$data['block_height'] && !$data['network_storage_power']) {
                throw new Exception('No valid data found in the response');
            }

            // Store the scraped data in database
            $this->store_scraped_data($data);

            return [
                'success' => true,
                'data' => $data
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Parse HTML content to extract all network statistics for real-time display
     */
    private function parse_filfox_realtime_html($html) {
        // Initialize all data fields
        $data = [
            'block_height' => null,
            'latest_block' => null,
            'network_storage_power' => null,
            'active_miners' => null,
            'block_reward' => null,
            'mining_reward' => null,
            'fil_production_24h' => null,
            'sector_initial_pledge' => null,
            'total_pledge_collateral' => null,
            'messages_24h' => null,
            'scraped_at' => current_time('mysql')
        ];

        // Remove script and style tags to avoid false matches
        $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);
        $html = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $html);

        // More flexible and comprehensive pattern matching

        // 1. Block Height - multiple patterns
        $block_height_patterns = [
            '/Block\s+Height[^>]*>.*?([0-9,]+)/is',
            '/Height[^>]*>.*?([0-9,]+)/is',
            '/block[^>]*height[^>]*>.*?([0-9,]+)/is',
            '/([0-9]{1,3}(?:,[0-9]{3}){2,})/s' // Large numbers with commas (likely block height)
        ];

        foreach ($block_height_patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $potential_height = (int) str_replace(',', '', $matches[1]);
                if ($potential_height > 5000000) { // Current block height should be > 5M
                    $data['block_height'] = $potential_height;
                    break;
                }
            }
        }

        // 2. Network Storage Power (EiB) - multiple patterns
        $storage_patterns = [
            '/Network\s+Storage\s+Power[^>]*>.*?([0-9]+\.?[0-9]*)\s*EiB/is',
            '/Storage\s+Power[^>]*>.*?([0-9]+\.?[0-9]*)\s*EiB/is',
            '/Power[^>]*>.*?([0-9]+\.?[0-9]*)\s*EiB/is',
            '/([0-9]+\.?[0-9]*)\s*EiB/is'
        ];

        foreach ($storage_patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $potential_storage = (float) $matches[1];
                if ($potential_storage > 10 && $potential_storage < 100) { // Reasonable range for EiB
                    $data['network_storage_power'] = $potential_storage;
                    break;
                }
            }
        }

        // 3. Active Miners - multiple patterns
        $miners_patterns = [
            '/Active\s+Miners[^>]*>.*?([0-9,]+)/is',
            '/Miners[^>]*>.*?([0-9,]+)/is',
            '/active[^>]*miners[^>]*>.*?([0-9,]+)/is'
        ];

        foreach ($miners_patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $potential_miners = (int) str_replace(',', '', $matches[1]);
                if ($potential_miners > 1000 && $potential_miners < 100000) { // Reasonable range
                    $data['active_miners'] = $potential_miners;
                    break;
                }
            }
        }

        // 4. Mining Reward (FIL/TiB) - multiple patterns
        $mining_reward_patterns = [
            '/24h\s+Average\s+Mining\s+Reward[^0-9]*([0-9]+\.?[0-9]*)\s*FIL\/TiB/is',
            '/Mining\s+Reward[^0-9]*([0-9]+\.?[0-9]*)\s*FIL\/TiB/is',
            '/Reward[^0-9]*([0-9]+\.?[0-9]*)\s*FIL\/TiB/is',
            '/([0-9]*\.?[0-9]+)\s*FIL\/TiB/is'
        ];

        foreach ($mining_reward_patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $potential_reward = (float) $matches[1];
                if ($potential_reward > 0 && $potential_reward < 10) { // Reasonable range for FIL/TiB
                    $data['mining_reward'] = $potential_reward;
                    break;
                }
            }
        }

        // 5. Block Reward - multiple patterns
        $block_reward_patterns = [
            '/Block\s+Reward[^>]*>.*?([0-9]+\.?[0-9]*)\s*FIL/is',
            '/Reward[^>]*>.*?([0-9]+\.?[0-9]*)\s*FIL(?!\/)/is',
            '/([0-9]+\.[0-9]{4})\s*FIL(?!\/)/is'
        ];

        foreach ($block_reward_patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $potential_reward = (float) $matches[1];
                if ($potential_reward > 1 && $potential_reward < 10) { // Reasonable range for block reward
                    $data['block_reward'] = $potential_reward;
                    break;
                }
            }
        }

        // 6. 24h FIL Production - multiple patterns
        $production_patterns = [
            '/24h\s+FIL\s+Production[^>]*>.*?([0-9,]+)\s*FIL/is',
            '/Production[^>]*>.*?([0-9,]+)\s*FIL/is',
            '/([0-9]{2,3}(?:,[0-9]{3})*)\s*FIL(?!\/)/is'
        ];

        foreach ($production_patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $potential_production = (int) str_replace(',', '', $matches[1]);
                if ($potential_production > 50000 && $potential_production < 200000) { // Reasonable range
                    $data['fil_production_24h'] = $potential_production;
                    break;
                }
            }
        }

        // 7. Total Pledge Collateral - multiple patterns
        $collateral_patterns = [
            '/Total\s+Pledge\s+Collateral[^>]*>.*?([0-9,]+)\s*FIL/is',
            '/Pledge\s+Collateral[^>]*>.*?([0-9,]+)\s*FIL/is',
            '/Collateral[^>]*>.*?([0-9,]+)\s*FIL/is',
            '/([0-9]{2,3}(?:,[0-9]{3}){2,})\s*FIL/is'
        ];

        foreach ($collateral_patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $potential_collateral = (int) str_replace(',', '', $matches[1]);
                if ($potential_collateral > 100000000) { // Should be > 100M
                    $data['total_pledge_collateral'] = $potential_collateral;
                    break;
                }
            }
        }

        // 8. 24h Messages - multiple patterns
        $messages_patterns = [
            '/24h\s+Messages[^>]*>.*?([0-9,]+)/is',
            '/Messages[^>]*>.*?([0-9,]+)/is',
            '/([0-9]{2,3}(?:,[0-9]{3})+)(?!\s*FIL)/is'
        ];

        foreach ($messages_patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $potential_messages = (int) str_replace(',', '', $matches[1]);
                if ($potential_messages > 100000 && $potential_messages < 1000000) { // Reasonable range
                    $data['messages_24h'] = $potential_messages;
                    break;
                }
            }
        }

        // 9. Sector Initial Pledge - multiple patterns
        $sector_pledge_patterns = [
            '/Current\s+Sector\s+Initial\s+Pledge[^>]*>.*?([0-9]+\.?[0-9]*)\s*FIL\/32GiB/is',
            '/Sector\s+Initial\s+Pledge[^>]*>.*?([0-9]+\.?[0-9]*)\s*FIL\/32GiB/is',
            '/Initial\s+Pledge[^>]*>.*?([0-9]+\.?[0-9]*)\s*FIL\/32GiB/is'
        ];

        foreach ($sector_pledge_patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $potential_pledge = (float) $matches[1];
                if ($potential_pledge > 0 && $potential_pledge < 1) { // Reasonable range for FIL/32GiB
                    $data['sector_initial_pledge'] = $potential_pledge;
                    break;
                }
            }
        }

        // 10. Latest Block (time ago) - multiple patterns
        $latest_block_patterns = [
            '/Latest\s+Block[^>]*>.*?([0-9]+\s*(?:min|sec|hour|day)s?\s*ago)/is',
            '/Block[^>]*>.*?([0-9]+\s*(?:min|sec|hour|day)s?\s*ago)/is',
            '/([0-9]+\s*(?:min|sec|hour|day)s?\s*ago)/is'
        ];

        foreach ($latest_block_patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $data['latest_block'] = trim($matches[1]);
                break;
            }
        }

        // Additional fallback: Look for any remaining large numbers that might be relevant
        if (!$data['block_height']) {
            // Look for the largest number in the HTML (likely block height)
            if (preg_match_all('/([0-9]{1,3}(?:,[0-9]{3}){2,})/s', $html, $matches)) {
                $largest_number = 0;
                foreach ($matches[1] as $match) {
                    $number = (int) str_replace(',', '', $match);
                    if ($number > $largest_number && $number > 5000000) {
                        $largest_number = $number;
                    }
                }
                if ($largest_number > 0) {
                    $data['block_height'] = $largest_number;
                }
            }
        }

        return $data;
    }

    /**
     * Upload KYC image to WordPress media library
     */
    public function upload_kyc_image($request) {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            return new WP_REST_Response([
                'success' => false,
                'error_code' => 'user_not_logged_in'
            ], 401);
        }

        // Check if files were uploaded
        if (empty($_FILES)) {
            return new WP_REST_Response([
                'success' => false,
                'error_code' => 'no_image_uploaded'
            ], 400);
        }

        $uploaded_files = [];
        $errors = [];

        // Process each uploaded file
        foreach ($_FILES as $file_key => $file) {
            if ($file['error'] !== UPLOAD_ERR_OK) {
                $errors[] = "Error uploading {$file_key}: " . $this->get_upload_error_message($file['error']);
                continue;
            }

            // Validate file type
            $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!in_array($file['type'], $allowed_types)) {
                $errors[] = "Invalid file type for {$file_key}. Only JPEG, PNG, and GIF are allowed.";
                continue;
            }

            // Validate file size (max 5MB)
            $max_size = 5 * 1024 * 1024; // 5MB
            if ($file['size'] > $max_size) {
                $errors[] = "File {$file_key} is too large. Maximum size is 5MB.";
                continue;
            }

            // Use WordPress media handling
            require_once(ABSPATH . 'wp-admin/includes/file.php');
            require_once(ABSPATH . 'wp-admin/includes/media.php');
            require_once(ABSPATH . 'wp-admin/includes/image.php');

            // Handle the upload
            $upload_overrides = [
                'test_form' => false,
                'unique_filename_callback' => function($dir, $name, $ext) {
                    $user_id = get_current_user_id();
                    $timestamp = time();
                    return "kyc_{$user_id}_{$timestamp}_{$name}";
                }
            ];

            $uploaded_file = wp_handle_upload($file, $upload_overrides);

            if (isset($uploaded_file['error'])) {
                $errors[] = "Error uploading {$file_key}: " . $uploaded_file['error'];
                continue;
            }

            // Create attachment post
            $attachment = [
                'post_mime_type' => $uploaded_file['type'],
                'post_title'     => sanitize_file_name(pathinfo($uploaded_file['file'], PATHINFO_FILENAME)),
                'post_content'   => '',
                'post_status'    => 'inherit'
            ];

            $attachment_id = wp_insert_attachment($attachment, $uploaded_file['file']);

            if (is_wp_error($attachment_id)) {
                $errors[] = "Error creating attachment for {$file_key}: " . $attachment_id->get_error_message();
                continue;
            }

            // Generate attachment metadata
            $attachment_data = wp_generate_attachment_metadata($attachment_id, $uploaded_file['file']);
            wp_update_attachment_metadata($attachment_id, $attachment_data);

            $uploaded_files[$file_key] = [
                'attachment_id' => $attachment_id,
                'url' => $uploaded_file['url'],
                'file' => $uploaded_file['file']
            ];
        }

        if (!empty($errors) && empty($uploaded_files)) {
            return new WP_REST_Response([
                'success' => false,
                'error_code' => 'upload_failed',
                'errors' => $errors
            ], 400);
        }

        return new WP_REST_Response([
            'success' => true,
            'message' => 'Files uploaded successfully',
            'error_code' => 'upload_successful',
            'files' => $uploaded_files,
            'errors' => $errors
        ], 200);
    }

    /**
     * Upload general image to WordPress media library
     */
    public function upload_image($request) {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            return new WP_REST_Response([
                'success' => false,
                'error_code' => 'user_not_logged_in'
            ], 401);
        }

        // Check if files were uploaded
        if (empty($_FILES)) {
            return new WP_REST_Response([
                'success' => false,
                'error_code' => 'no_image_uploaded'
            ], 400);
        }

        $uploaded_files = [];
        $errors = [];

        // Process each uploaded file
        foreach ($_FILES as $file_key => $file) {
            if ($file['error'] !== UPLOAD_ERR_OK) {
                $errors[] = "Error uploading {$file_key}: " . $this->get_upload_error_message($file['error']);
                continue;
            }

            // Validate file type
            $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!in_array($file['type'], $allowed_types)) {
                $errors[] = "Invalid file type for {$file_key}. Only JPEG, PNG, and GIF are allowed.";
                continue;
            }

            // Validate file size (max 5MB)
            $max_size = 5 * 1024 * 1024; // 5MB
            if ($file['size'] > $max_size) {
                $errors[] = "File {$file_key} is too large. Maximum size is 5MB.";
                continue;
            }

            // Use WordPress media handling
            require_once(ABSPATH . 'wp-admin/includes/file.php');
            require_once(ABSPATH . 'wp-admin/includes/media.php');
            require_once(ABSPATH . 'wp-admin/includes/image.php');

            // Handle the upload
            $upload_overrides = [
                'test_form' => false,
                'unique_filename_callback' => function($dir, $name, $ext) {
                    $user_id = get_current_user_id();
                    $timestamp = time();
                    return "order_{$user_id}_{$timestamp}_{$name}";
                }
            ];

            $uploaded_file = wp_handle_upload($file, $upload_overrides);

            if (isset($uploaded_file['error'])) {
                $errors[] = "Error uploading {$file_key}: " . $uploaded_file['error'];
                continue;
            }

            // Create attachment post
            $attachment = [
                'post_mime_type' => $uploaded_file['type'],
                'post_title'     => sanitize_file_name(pathinfo($uploaded_file['file'], PATHINFO_FILENAME)),
                'post_content'   => '',
                'post_status'    => 'inherit'
            ];

            $attachment_id = wp_insert_attachment($attachment, $uploaded_file['file']);

            if (is_wp_error($attachment_id)) {
                $errors[] = "Error creating attachment for {$file_key}: " . $attachment_id->get_error_message();
                continue;
            }

            // Generate attachment metadata
            $attachment_data = wp_generate_attachment_metadata($attachment_id, $uploaded_file['file']);
            wp_update_attachment_metadata($attachment_id, $attachment_data);

            $uploaded_files[$file_key] = [
                'attachment_id' => $attachment_id,
                'url' => $uploaded_file['url'],
                'file' => $uploaded_file['file']
            ];
        }

        if (!empty($errors) && empty($uploaded_files)) {
            return new WP_REST_Response([
                'success' => false,
                'error_code' => 'upload_failed',
                'errors' => $errors
            ], 400);
        }

        return new WP_REST_Response([
            'success' => true,
            'message' => 'Files uploaded successfully',
            'error_code' => 'upload_successful',
            'files' => $uploaded_files,
            'errors' => $errors
        ], 200);
    }

    /**
     * Get upload error message
     */
    private function get_upload_error_message($error_code) {
        switch ($error_code) {
            case UPLOAD_ERR_INI_SIZE:
                return 'The uploaded file exceeds the upload_max_filesize directive in php.ini';
            case UPLOAD_ERR_FORM_SIZE:
                return 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form';
            case UPLOAD_ERR_PARTIAL:
                return 'The uploaded file was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing a temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'A PHP extension stopped the file upload';
            default:
                return 'Unknown upload error';
        }
    }

    /**
     * Submit KYC data with file uploads
     */
    public function submit_kyc($request) {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'User not logged in',
                'error_code' => 'user_not_logged_in'
            ], 401);
        }

        $current_user = wp_get_current_user();
        $user_email = $current_user->user_email;

        // Get form data
        $real_name = sanitize_text_field($request->get_param('real_name'));
        $id_number = sanitize_text_field($request->get_param('id_number'));

        if (empty($real_name) || empty($id_number)) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Real name and ID number are required',
                'error_code' => 'real_name_id_number_required'
            ], 400);
        }

        $uploaded_files = [];
        $errors = [];

        // Handle file uploads if present
        if (!empty($_FILES)) {
            foreach ($_FILES as $file_key => $file) {
                if ($file['error'] !== UPLOAD_ERR_OK) {
                    if ($file['error'] !== UPLOAD_ERR_NO_FILE) {
                        $errors[] = "Error uploading {$file_key}: " . $this->get_upload_error_message($file['error']);
                    }
                    continue;
                }

                // Validate file type
                $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                if (!in_array($file['type'], $allowed_types)) {
                    $errors[] = "Invalid file type for {$file_key}. Only JPEG, PNG, and GIF are allowed.";
                    continue;
                }

                // Validate file size (max 5MB)
                $max_size = 5 * 1024 * 1024; // 5MB
                if ($file['size'] > $max_size) {
                    $errors[] = "File {$file_key} is too large. Maximum size is 5MB.";
                    continue;
                }

                // Use WordPress media handling
                require_once(ABSPATH . 'wp-admin/includes/file.php');
                require_once(ABSPATH . 'wp-admin/includes/media.php');
                require_once(ABSPATH . 'wp-admin/includes/image.php');

                // Handle the upload
                $upload_overrides = [
                    'test_form' => false,
                    'unique_filename_callback' => function($dir, $name, $ext) use ($user_email) {
                        $timestamp = time();
                        $safe_email = sanitize_file_name(str_replace('@', '_at_', $user_email));
                        return "kyc_{$safe_email}_{$timestamp}_{$name}";
                    }
                ];

                $uploaded_file = wp_handle_upload($file, $upload_overrides);

                if (isset($uploaded_file['error'])) {
                    $errors[] = "Error uploading {$file_key}: " . $uploaded_file['error'];
                    continue;
                }

                // Create attachment post
                $attachment = [
                    'post_mime_type' => $uploaded_file['type'],
                    'post_title'     => sanitize_file_name(pathinfo($uploaded_file['file'], PATHINFO_FILENAME)),
                    'post_content'   => '',
                    'post_status'    => 'inherit'
                ];

                $attachment_id = wp_insert_attachment($attachment, $uploaded_file['file']);

                if (is_wp_error($attachment_id)) {
                    $errors[] = "Error creating attachment for {$file_key}: " . $attachment_id->get_error_message();
                    continue;
                }

                // Generate attachment metadata
                $attachment_data = wp_generate_attachment_metadata($attachment_id, $uploaded_file['file']);
                wp_update_attachment_metadata($attachment_id, $attachment_data);

                $uploaded_files[$file_key] = [
                    'attachment_id' => $attachment_id,
                    'url' => $uploaded_file['url'],
                    'file' => $uploaded_file['file']
                ];
            }
        }

        // If there were upload errors and no files were uploaded successfully, return error
        if (!empty($errors) && empty($uploaded_files)) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'File upload failed',
                'error_code' => 'upload_failed',
                'errors' => $errors
            ], 400);
        }

        // Get user_id from request
        $user_id = sanitize_text_field($request->get_param('user_id'));
        if (empty($user_id)) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'User ID is required',
                'error_code' => 'user_id_required'
            ], 400);
        }

        // Now update Supabase with the KYC data
        try {
            $supabase_url = get_option('fil_platform_supabase_url');
            $supabase_service_key = get_option('fil_platform_supabase_service_key');

            if (empty($supabase_url) || empty($supabase_service_key)) {
                throw new Exception('Supabase configuration not found');
            }

            $supabase_user_id = $user_id;

            // Prepare KYC data for Supabase
            $kyc_data = [
                'user_id' => $supabase_user_id,
                'real_name' => $real_name,
                'id_number' => $id_number,
                'verify_status' => 'pending',
                'kyc_submitted_at' => gmdate('Y-m-d\TH:i:s.u\Z') // Current UTC timestamp
            ];

            // Add image URLs if uploaded
            if (isset($uploaded_files['id_img_front'])) {
                $kyc_data['id_img_front'] = $uploaded_files['id_img_front']['url'];
            }

            if (isset($uploaded_files['id_img_back'])) {
                $kyc_data['id_img_back'] = $uploaded_files['id_img_back']['url'];
            }

            // Use UPSERT to insert or update customer profile
            $supabase_response = wp_remote_post($supabase_url . '/rest/v1/customer_profiles', [
                'method' => 'POST',
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json',
                    'Prefer' => 'resolution=merge-duplicates,return=representation'
                ],
                'body' => json_encode($kyc_data)
            ]);

            if (is_wp_error($supabase_response)) {
                throw new Exception('Failed to update Supabase: ' . $supabase_response->get_error_message());
            }

            $response_code = wp_remote_retrieve_response_code($supabase_response);
            if ($response_code >= 400) {
                $error_body = wp_remote_retrieve_body($supabase_response);
                throw new Exception('Supabase error: ' . $error_body);
            }

            return new WP_REST_Response([
                'success' => true,
                'message' => 'KYC submitted successfully',
                'files' => $uploaded_files,
                'errors' => $errors
            ], 200);

        } catch (Exception $e) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Failed to submit KYC: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new member (customer)
     */
    public function create_member($request) {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'User not logged in'
            ], 401);
        }

        // Get request data
        $email = sanitize_email($request->get_param('email'));
        $password = $request->get_param('password');
        $invite_code = sanitize_text_field($request->get_param('invite_code'));
        $agent_id = sanitize_text_field($request->get_param('agent_id')); // Optional for makers

        // Validate required fields (agent_id is optional)
        if (empty($email) || empty($password) || empty($invite_code)) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Email, password, and invite code are required'
            ], 400);
        }

        // Validate email format
        if (!is_email($email)) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Invalid email format'
            ], 400);
        }

        // Validate password length
        if (strlen($password) < 6) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Password must be at least 6 characters long'
            ], 400);
        }

        try {
            $supabase_url = get_option('fil_platform_supabase_url');
            $supabase_service_key = get_option('fil_platform_supabase_service_key');

            if (empty($supabase_url) || empty($supabase_service_key)) {
                throw new Exception('Supabase configuration not found');
            }

            // Get current user's role to determine if agent_id should be set
            $current_user = wp_get_current_user();
            $current_user_email = $current_user->user_email;

            // Get current user's role from Supabase
            $current_user_response = wp_remote_get($supabase_url . '/rest/v1/users?email=eq.' . urlencode($current_user_email), [
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json'
                ]
            ]);

            if (is_wp_error($current_user_response)) {
                throw new Exception('Failed to fetch current user data: ' . $current_user_response->get_error_message());
            }

            $current_user_data = json_decode(wp_remote_retrieve_body($current_user_response), true);
            if (empty($current_user_data)) {
                throw new Exception('Current user not found in database');
            }

            $current_user_role = $current_user_data[0]['role'];
            $current_user_id = $current_user_data[0]['id'];

            // Step 1: Create auth user using Supabase Admin API
            // Try the admin API first
            $auth_response = wp_remote_post($supabase_url . '/auth/v1/admin/users', [
                'method' => 'POST',
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json'
                ],
                'body' => json_encode([
                    'email' => $email,
                    'password' => $password,
                    'email_confirm' => true, // Auto-confirm email
                    'user_metadata' => [
                        'role' => 'customer'
                    ]
                ])
            ]);

            if (is_wp_error($auth_response)) {
                throw new Exception('Failed to create auth user: ' . $auth_response->get_error_message());
            }

            $auth_response_code = wp_remote_retrieve_response_code($auth_response);
            if ($auth_response_code >= 400) {
                $error_body = wp_remote_retrieve_body($auth_response);
                $error_data = json_decode($error_body, true);
                $error_message = isset($error_data['message']) ? $error_data['message'] : 'Failed to create auth user';
                throw new Exception('Auth user creation error: ' . $error_message);
            }

            $auth_response_body = wp_remote_retrieve_body($auth_response);
            $auth_data = json_decode($auth_response_body, true);

            $new_user_id = null;

            // Try different possible response structures
            // Based on Supabase documentation, admin API should return direct format
            if (isset($auth_data['id'])) {
                // Direct format: { "id": "uuid", ... } - This is the expected format for admin API
                $new_user_id = $auth_data['id'];
            } elseif (isset($auth_data['user']['id'])) {
                // Standard format: { "user": { "id": "uuid", ... } } - This is for regular signup
                $new_user_id = $auth_data['user']['id'];
            } elseif (isset($auth_data['data']['user']['id'])) {
                // Nested format: { "data": { "user": { "id": "uuid", ... } } }
                $new_user_id = $auth_data['data']['user']['id'];
            }

            if (empty($new_user_id)) {
                // For debugging: return the full response to understand the structure
                return new WP_REST_Response([
                    'success' => false,
                    'message' => 'Failed to extract user ID from Supabase Auth API response',
                    'debug_info' => [
                        'response_code' => $auth_response_code,
                        'response_body' => $auth_response_body,
                        'parsed_data' => $auth_data,
                        'email' => $email,
                        'supabase_url' => $supabase_url
                    ]
                ], 400);
            }

            // Validate that we have a valid UUID
            if (!preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $new_user_id)) {
                throw new Exception('Invalid user ID format received: ' . $new_user_id);
            }

            // Step 2: Create public.users record
            $users_data = [
                'id' => $new_user_id,
                'email' => $email,
                'role' => 'customer',
                'invite_code' => $invite_code,
                'referred_by' => $current_user_id
            ];


            $users_response = wp_remote_post($supabase_url . '/rest/v1/users', [
                'method' => 'POST',
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json',
                    'Prefer' => 'return=representation'
                ],
                'body' => json_encode($users_data)
            ]);

            if (is_wp_error($users_response)) {
                error_log('FIL Platform: WP Error creating users record: ' . $users_response->get_error_message());
                throw new Exception('Failed to create users record: ' . $users_response->get_error_message());
            }

            $users_response_code = wp_remote_retrieve_response_code($users_response);
            $users_response_body = wp_remote_retrieve_body($users_response);

            if ($users_response_code >= 400) {
                throw new Exception('Users record creation error (HTTP ' . $users_response_code . '): ' . $users_response_body);
            }

            // Step 3: Create customer_profiles record
            $profile_data = [
                'user_id' => $new_user_id,
                'verify_status' => 'not_submitted'
            ];

            // Only set agent_id if the current user is an agent
            if ($current_user_role === 'agent' && !empty($agent_id)) {
                $profile_data['agent_id'] = $agent_id;
            }

            $profile_response = wp_remote_post($supabase_url . '/rest/v1/customer_profiles', [
                'method' => 'POST',
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json',
                    'Prefer' => 'return=representation'
                ],
                'body' => json_encode($profile_data)
            ]);

            if (is_wp_error($profile_response)) {
                throw new Exception('Failed to create customer profile: ' . $profile_response->get_error_message());
            }

            $profile_response_code = wp_remote_retrieve_response_code($profile_response);
            if ($profile_response_code >= 400) {
                $error_body = wp_remote_retrieve_body($profile_response);
                throw new Exception('Customer profile creation error: ' . $error_body);
            }

            // Step 4: Create user_assets record
            $assets_response = wp_remote_post($supabase_url . '/rest/v1/user_assets', [
                'method' => 'POST',
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json',
                    'Prefer' => 'return=representation'
                ],
                'body' => json_encode([
                    'user_id' => $new_user_id,
                    'currency_code' => 'FIL',
                    'balance_available' => 0,
                    'balance_locked' => 0,
                    'balance_total' => 0,
                    'withdrawn_total' => 0
                ])
            ]);

            if (is_wp_error($assets_response)) {
                throw new Exception('Failed to create user assets: ' . $assets_response->get_error_message());
            }

            $assets_response_code = wp_remote_retrieve_response_code($assets_response);
            if ($assets_response_code >= 400) {
                $error_body = wp_remote_retrieve_body($assets_response);
                throw new Exception('User assets creation error: ' . $error_body);
            }

            return new WP_REST_Response([
                'success' => true,
                'message' => 'Member created successfully',
                'user_id' => $new_user_id,
                'email' => $email
            ], 200);

        } catch (Exception $e) {
            error_log('FIL Platform: Create member error: ' . $e->getMessage());
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Failed to create member: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Register a new customer using invite code (public endpoint)
     */
    public function register_customer($request) {
        // Get request data
        $email = sanitize_email($request->get_param('email'));
        $password = $request->get_param('password');
        $invite_code = sanitize_text_field($request->get_param('invite_code'));

        // Validate required fields
        if (empty($email) || empty($password) || empty($invite_code)) {
            return new WP_REST_Response([
                'success' => false,
                'error_code' => 'all_fields_required',
                'message' => 'All fields are required'
            ], 400);
        }

        // Validate email format
        if (!is_email($email)) {
            return new WP_REST_Response([
                'success' => false,
                'error_code' => 'invalid_email_format',
                'message' => 'Invalid email format'
            ], 400);
        }

        // Validate password length
        if (strlen($password) < 6) {
            return new WP_REST_Response([
                'success' => false,
                'error_code' => 'password_min_length',
                'message' => 'Password must be at least 6 characters long'
            ], 400);
        }

        try {
            $supabase_url = get_option('fil_platform_supabase_url');
            $supabase_service_key = get_option('fil_platform_supabase_service_key');

            if (empty($supabase_url) || empty($supabase_service_key)) {
                throw new Exception('Supabase configuration not found');
            }

            // Step 1: Validate invite code and get referrer info
            $invite_response = wp_remote_get($supabase_url . '/rest/v1/users?select=id,role&invite_code=eq.' . urlencode($invite_code), [
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json'
                ]
            ]);

            if (is_wp_error($invite_response)) {
                throw new Exception('Failed to validate invite code: ' . $invite_response->get_error_message());
            }

            $invite_response_code = wp_remote_retrieve_response_code($invite_response);
            if ($invite_response_code >= 400) {
                throw new Exception('Invite code validation error: ' . wp_remote_retrieve_body($invite_response));
            }

            $invite_data = json_decode(wp_remote_retrieve_body($invite_response), true);
            if (empty($invite_data)) {
                return new WP_REST_Response([
                    'success' => false,
                    'error_code' => 'invalid_invite_code',
                    'message' => 'Invalid invite code'
                ], 400);
            }

            $referrer_id = $invite_data[0]['id'];
            $referrer_role = $invite_data[0]['role'];

            // Step 2: Check if email already exists
            $email_check_response = wp_remote_get($supabase_url . '/rest/v1/users?email=eq.' . urlencode($email), [
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json'
                ]
            ]);

            if (!is_wp_error($email_check_response)) {
                $email_check_data = json_decode(wp_remote_retrieve_body($email_check_response), true);
                if (!empty($email_check_data)) {
                    return new WP_REST_Response([
                        'success' => false,
                        'error_code' => 'email_already_exists',
                        'message' => 'Email already exists'
                    ], 400);
                }
            }

            // Step 3: Create auth user using Supabase Admin API
            $auth_response = wp_remote_post($supabase_url . '/auth/v1/admin/users', [
                'method' => 'POST',
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json'
                ],
                'body' => json_encode([
                    'email' => $email,
                    'password' => $password,
                    'email_confirm' => true, // Auto-confirm email
                    'user_metadata' => [
                        'role' => 'customer'
                    ]
                ])
            ]);

            if (is_wp_error($auth_response)) {
                throw new Exception('Failed to create auth user: ' . $auth_response->get_error_message());
            }

            $auth_response_code = wp_remote_retrieve_response_code($auth_response);
            if ($auth_response_code >= 400) {
                $error_body = wp_remote_retrieve_body($auth_response);
                $error_data = json_decode($error_body, true);

                if (isset($error_data['message']) && strpos($error_data['message'], 'already registered') !== false) {
                    return new WP_REST_Response([
                        'success' => false,
                        'error_code' => 'email_already_exists',
                        'message' => 'Email already exists'
                    ], 400);
                }

                throw new Exception('Auth user creation error: ' . $error_body);
            }

            $auth_data = json_decode(wp_remote_retrieve_body($auth_response), true);
            $new_user_id = $auth_data['id'];

            // Generate unique invite code for new user
            $new_invite_code = strtoupper(substr(md5($email . time()), 0, 8));

            // Step 4: Create public.users record
            $users_data = [
                'id' => $new_user_id,
                'email' => $email,
                'role' => 'customer',
                'invite_code' => $new_invite_code,
                'referred_by' => $referrer_id
            ];

            $users_response = wp_remote_post($supabase_url . '/rest/v1/users', [
                'method' => 'POST',
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json',
                    'Prefer' => 'return=representation'
                ],
                'body' => json_encode($users_data)
            ]);

            if (is_wp_error($users_response)) {
                throw new Exception('Failed to create users record: ' . $users_response->get_error_message());
            }

            $users_response_code = wp_remote_retrieve_response_code($users_response);
            if ($users_response_code >= 400) {
                $error_body = wp_remote_retrieve_body($users_response);
                throw new Exception('Users record creation error: ' . $error_body);
            }

            // Step 5: Create customer_profiles record
            // Only set agent_id if the referrer is an agent
            $profile_data = [
                'user_id' => $new_user_id,
                'verify_status' => 'not_submitted'
            ];

            if ($referrer_role === 'agent') {
                $profile_data['agent_id'] = $referrer_id;
            }

            $profile_response = wp_remote_post($supabase_url . '/rest/v1/customer_profiles', [
                'method' => 'POST',
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json',
                    'Prefer' => 'return=representation'
                ],
                'body' => json_encode($profile_data)
            ]);

            if (is_wp_error($profile_response)) {
                throw new Exception('Failed to create customer profile: ' . $profile_response->get_error_message());
            }

            $profile_response_code = wp_remote_retrieve_response_code($profile_response);
            if ($profile_response_code >= 400) {
                $error_body = wp_remote_retrieve_body($profile_response);
                throw new Exception('Customer profile creation error: ' . $error_body);
            }

            // Step 6: Create user_assets record
            $assets_response = wp_remote_post($supabase_url . '/rest/v1/user_assets', [
                'method' => 'POST',
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json',
                    'Prefer' => 'return=representation'
                ],
                'body' => json_encode([
                    'user_id' => $new_user_id,
                    'currency_code' => 'FIL',
                    'balance_available' => 0,
                    'balance_locked' => 0,
                    'balance_total' => 0,
                    'withdrawn_total' => 0
                ])
            ]);

            if (is_wp_error($assets_response)) {
                throw new Exception('Failed to create user assets: ' . $assets_response->get_error_message());
            }

            $assets_response_code = wp_remote_retrieve_response_code($assets_response);
            if ($assets_response_code >= 400) {
                $error_body = wp_remote_retrieve_body($assets_response);
                throw new Exception('User assets creation error: ' . $error_body);
            }

            return new WP_REST_Response([
                'success' => true,
                'message' => 'Registration successful',
                'user_id' => $new_user_id,
                'email' => $email
            ], 200);

        } catch (Exception $e) {
            error_log('FIL Platform: Register customer error: ' . $e->getMessage());
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Registration failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test auth API for debugging
     */
    public function test_auth($request) {
        try {
            $supabase_url = get_option('fil_platform_supabase_url');
            $supabase_service_key = get_option('fil_platform_supabase_service_key');

            if (empty($supabase_url) || empty($supabase_service_key)) {
                throw new Exception('Supabase configuration not found');
            }

            // Test admin API
            $test_email = 'test-' . time() . '@example.com';
            $auth_response = wp_remote_post($supabase_url . '/auth/v1/admin/users', [
                'method' => 'POST',
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json'
                ],
                'body' => json_encode([
                    'email' => $test_email,
                    'password' => 'test123456',
                    'email_confirm' => true
                ])
            ]);

            $response_code = wp_remote_retrieve_response_code($auth_response);
            $response_body = wp_remote_retrieve_body($auth_response);

            return new WP_REST_Response([
                'success' => true,
                'test_email' => $test_email,
                'response_code' => $response_code,
                'response_body' => $response_body,
                'parsed_response' => json_decode($response_body, true)
            ], 200);

        } catch (Exception $e) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process withdrawal request
     */
    public function process_withdrawal($request) {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'User not logged in',
                'error_code' => 'user_not_logged_in'
            ], 401);
        }

        // Get request data
        $amount = floatval($request->get_param('amount'));
        $address = sanitize_text_field($request->get_param('address'));
        $withdraw_password = $request->get_param('withdraw_password');
        $remark = sanitize_text_field($request->get_param('remark'));

        // Validate required fields
        if (empty($amount) || empty($address) || empty($withdraw_password)) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'All fields are required',
                'error_code' => 'all_fields_required'
            ], 400);
        }

        // Validate amount
        if ($amount < 1) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Amount must be at least 1 FIL',
                'error_code' => 'amount_too_small'
            ], 400);
        }

        try {
            $supabase_url = get_option('fil_platform_supabase_url');
            $supabase_service_key = get_option('fil_platform_supabase_service_key');

            if (empty($supabase_url) || empty($supabase_service_key)) {
                throw new Exception('Supabase configuration not found');
            }

            // Get current user from request headers (Supabase auth token)
            $auth_header = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            if (empty($auth_header) || !str_starts_with($auth_header, 'Bearer ')) {
                // Fallback: try to get user by email from WordPress
                $current_user = wp_get_current_user();
                $user_email = $current_user->user_email;

                // Get the user's Supabase ID by email
                $users_response = wp_remote_get($supabase_url . '/rest/v1/users?email=eq.' . urlencode($user_email), [
                    'headers' => [
                        'apikey' => $supabase_service_key,
                        'Authorization' => 'Bearer ' . $supabase_service_key,
                        'Content-Type' => 'application/json'
                    ]
                ]);

                if (is_wp_error($users_response)) {
                    throw new Exception('Failed to fetch user data: ' . $users_response->get_error_message());
                }

                $users_data = json_decode(wp_remote_retrieve_body($users_response), true);
                if (empty($users_data)) {
                    throw new Exception('User not found in database');
                }

                $supabase_user_id = $users_data[0]['id'];
            } else {
                // Extract token and get user from Supabase auth
                $token = substr($auth_header, 7);

                // Get user from Supabase auth
                $auth_response = wp_remote_get($supabase_url . '/auth/v1/user', [
                    'headers' => [
                        'apikey' => $supabase_service_key,
                        'Authorization' => 'Bearer ' . $token,
                        'Content-Type' => 'application/json'
                    ]
                ]);

                if (is_wp_error($auth_response)) {
                    throw new Exception('Failed to authenticate user: ' . $auth_response->get_error_message());
                }

                $auth_data = json_decode(wp_remote_retrieve_body($auth_response), true);
                if (empty($auth_data) || !isset($auth_data['id'])) {
                    throw new Exception('Invalid authentication token');
                }

                $supabase_user_id = $auth_data['id'];
                $user_email = $auth_data['email'] ?? '';
            }

            // Get user's available balance
            $assets_response = wp_remote_get($supabase_url . '/rest/v1/user_assets?user_id=eq.' . $supabase_user_id . '&currency_code=eq.FIL', [
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json'
                ]
            ]);

            if (is_wp_error($assets_response)) {
                throw new Exception('Failed to fetch user assets: ' . $assets_response->get_error_message());
            }

            $assets_data = json_decode(wp_remote_retrieve_body($assets_response), true);

            if (empty($assets_data)) {
                throw new Exception('No FIL balance found for user ID: ' . $supabase_user_id);
            }

            $available_balance = floatval($assets_data[0]['balance_available']);

            // Check if amount is within limits
            if ($amount > $available_balance / 1.1) {
                return new WP_REST_Response([
                    'success' => false,
                    'message' => 'Amount exceeds available balance',
                    'error_code' => 'amount_too_large'
                ], 400);
            }

            // Get customer profile to verify withdraw password
            $profile_response = wp_remote_get($supabase_url . '/rest/v1/customer_profiles?user_id=eq.' . $supabase_user_id, [
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json'
                ]
            ]);

            if (is_wp_error($profile_response)) {
                throw new Exception('Failed to fetch customer profile: ' . $profile_response->get_error_message());
            }

            $profile_data = json_decode(wp_remote_retrieve_body($profile_response), true);
            if (empty($profile_data)) {
                throw new Exception('Customer profile not found');
            }

            $stored_password_hash = $profile_data[0]['withdraw_pwd_hash'];
            $agent_id = $profile_data[0]['agent_id'];

            // Verify withdraw password
            if (empty($stored_password_hash) || !password_verify($withdraw_password, $stored_password_hash)) {
                return new WP_REST_Response([
                    'success' => false,
                    'error_code' => 'withdraw_password_incorrect'
                ], 400);
            }

            // Get notification email (agent or maker support email)
            $notification_email = '';

            if (!empty($agent_id)) {
                // Customer has an agent, send to agent's email
                $agent_response = wp_remote_get($supabase_url . '/rest/v1/users?id=eq.' . $agent_id, [
                    'headers' => [
                        'apikey' => $supabase_service_key,
                        'Authorization' => 'Bearer ' . $supabase_service_key,
                        'Content-Type' => 'application/json'
                    ]
                ]);

                if (is_wp_error($agent_response)) {
                    throw new Exception('Failed to fetch agent data: ' . $agent_response->get_error_message());
                }

                $agent_data = json_decode(wp_remote_retrieve_body($agent_response), true);
                if (empty($agent_data)) {
                    throw new Exception('Agent not found');
                }

                $notification_email = $agent_data[0]['email'];
            } else {
                // Customer has no agent, send to maker's support email
                $maker_response = wp_remote_get($supabase_url . '/rest/v1/maker_profiles?select=support_email', [
                    'headers' => [
                        'apikey' => $supabase_service_key,
                        'Authorization' => 'Bearer ' . $supabase_service_key,
                        'Content-Type' => 'application/json'
                    ]
                ]);

                if (is_wp_error($maker_response)) {
                    throw new Exception('Failed to fetch maker data: ' . $maker_response->get_error_message());
                }

                $maker_data = json_decode(wp_remote_retrieve_body($maker_response), true);
                if (empty($maker_data) || empty($maker_data[0]['support_email'])) {
                    throw new Exception('Maker support email not found');
                }

                $notification_email = $maker_data[0]['support_email'];
            }

            // Save withdrawal request to database
            $withdrawal_data = [
                'user_id' => $supabase_user_id,
                'currency_code' => 'FIL',
                'request_amount' => $amount,
                'final_amount' => $amount - 0.01,
                'fee' => 0.01,
                'user_remark' => $remark,
                'status' => 'pending',
                'wallet_type' => 'total wallet',
                'address' => $address
            ];

            $withdrawal_response = wp_remote_post($supabase_url . '/rest/v1/withdrawals', [
                'method' => 'POST',
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json',
                    'Prefer' => 'return=representation'
                ],
                'body' => json_encode($withdrawal_data)
            ]);

            if (is_wp_error($withdrawal_response)) {
                throw new Exception('Failed to save withdrawal request: ' . $withdrawal_response->get_error_message());
            }

            $withdrawal_response_code = wp_remote_retrieve_response_code($withdrawal_response);
            if ($withdrawal_response_code >= 400) {
                $error_body = wp_remote_retrieve_body($withdrawal_response);
                throw new Exception('Failed to save withdrawal request: ' . $error_body);
            }

            // Send email to agent or maker support
            $this->send_withdrawal_notification_email($notification_email, $user_email, $amount, $address, $remark);

            return new WP_REST_Response([
                'success' => true,
                'message' => 'Withdrawal request sent successfully'
            ], 200);

        } catch (Exception $e) {
            error_log('FIL Platform: Withdrawal error: ' . $e->getMessage());
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Failed to process withdrawal: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Change member password using Supabase Admin API
     */
    public function change_member_password($request) {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'User not logged in',
                'error_code' => 'user_not_logged_in'
            ], 401);
        }

        // Get request parameters
        $user_id = sanitize_text_field($request->get_param('user_id'));
        $new_password = $request->get_param('new_password');

        // Validate required fields
        if (empty($user_id) || empty($new_password)) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'User ID and new password are required',
                'error_code' => 'missing_required_fields'
            ], 400);
        }

        // Validate password length
        if (strlen($new_password) < 6) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Password must be at least 6 characters long',
                'error_code' => 'password_too_short'
            ], 400);
        }

        // Validate user_id format (should be UUID)
        if (!preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $user_id)) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Invalid user ID format',
                'error_code' => 'invalid_user_id'
            ], 400);
        }

        try {
            $supabase_url = get_option('fil_platform_supabase_url');
            $supabase_service_key = get_option('fil_platform_supabase_service_key');

            if (empty($supabase_url) || empty($supabase_service_key)) {
                throw new Exception('Supabase configuration not found');
            }

            // Get current user for permission check
            $current_user = wp_get_current_user();
            $current_user_email = $current_user->user_email;

            // Get current user's role from Supabase to verify permissions
            $current_user_response = wp_remote_get($supabase_url . '/rest/v1/users?email=eq.' . urlencode($current_user_email), [
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json'
                ]
            ]);

            if (is_wp_error($current_user_response)) {
                throw new Exception('Failed to fetch current user data: ' . $current_user_response->get_error_message());
            }

            $current_user_data = json_decode(wp_remote_retrieve_body($current_user_response), true);
            if (empty($current_user_data)) {
                throw new Exception('Current user not found in database');
            }

            $current_user_role = $current_user_data[0]['role'];

            // Only allow makers and agents to change passwords
            if (!in_array($current_user_role, ['maker', 'agent'])) {
                return new WP_REST_Response([
                    'success' => false,
                    'message' => 'Insufficient permissions to change member password',
                    'error_code' => 'insufficient_permissions'
                ], 403);
            }

            // Use Supabase Admin API to update user password
            $auth_response = wp_remote_request($supabase_url . '/auth/v1/admin/users/' . $user_id, [
                'method' => 'PUT',
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json'
                ],
                'body' => json_encode([
                    'password' => $new_password
                ])
            ]);

            if (is_wp_error($auth_response)) {
                throw new Exception('Failed to update password: ' . $auth_response->get_error_message());
            }

            $auth_response_code = wp_remote_retrieve_response_code($auth_response);
            if ($auth_response_code >= 400) {
                $error_body = wp_remote_retrieve_body($auth_response);
                $error_data = json_decode($error_body, true);
                $error_message = isset($error_data['message']) ? $error_data['message'] : 'Failed to update password';
                throw new Exception('Password update error: ' . $error_message);
            }

            // Log the password change action for audit purposes
            $current_user_id = $current_user_data[0]['id'];
            $audit_log_data = [
                'user_id' => $current_user_id,
                'action' => 'password_changed',
                'object_table' => 'auth.users',
                'object_id' => $user_id,
                'diff' => [
                    'old' => ['password' => '[REDACTED]'],
                    'new' => ['password' => '[REDACTED]']
                ]
            ];

            $audit_response = wp_remote_post($supabase_url . '/rest/v1/audit_logs', [
                'method' => 'POST',
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json'
                ],
                'body' => json_encode($audit_log_data)
            ]);

            // Don't fail the main operation if audit log fails
            if (is_wp_error($audit_response)) {
                error_log('FIL Platform: Failed to log password change audit: ' . $audit_response->get_error_message());
            }

            return new WP_REST_Response([
                'success' => true,
                'message' => 'Password changed successfully',
                'error_code' => 'password_changed_successfully'
            ], 200);

        } catch (Exception $e) {
            error_log('FIL Platform: Password change error: ' . $e->getMessage());
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Failed to change password: ' . $e->getMessage(),
                'error_code' => 'password_change_failed'
            ], 500);
        }
    }

    /**
     * Send withdrawal notification email to agent or maker support
     */
    private function send_withdrawal_notification_email($notification_email, $customer_email, $amount, $address, $remark) {
        $subject = 'Withdrawal Request - ' . $customer_email;

        $message = "
        <h2>出金リクエスト通知</h2>
        <p>お客様より出金リクエストがありました：</p>
        <ul>
            <li><strong>お客様のメールアドレス：</strong> {$customer_email}</li>
            <li><strong>出金金額：</strong> {$amount} FIL</li>
            <li><strong>出金先アドレス：</strong> {$address}</li>
            <li><strong>出金備考：</strong> {$remark}</li>
            <li><strong>リクエスト日時：</strong> " . current_time('mysql') . "</li>
        </ul>
        <p>お手数ですが、できるだけ早くご対応をお願いいたします。</p>
        ";

        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: FIL Platform <noreply@' . $_SERVER['HTTP_HOST'] . '>'
        );

        wp_mail($notification_email, $subject, $message, $headers);
    }

    /**
     * Simple test endpoint for debugging
     */
    public function debug_endpoint($request) {
        error_log('FIL Platform: Debug endpoint called');
        return new WP_REST_Response([
            'success' => true,
            'message' => 'Debug endpoint is working',
            'timestamp' => current_time('mysql'),
            'api_version' => 'fil-platform/v1'
        ], 200);
    }

    /**
     * Store scraped data in Supabase
     */
    private function store_scraped_data($data) {
        try {
            $supabase_url = get_option('fil_platform_supabase_url');
            $supabase_service_key = get_option('fil_platform_supabase_service_key');

            if (!$supabase_url || !$supabase_service_key) {
                error_log('FIL Platform: Missing Supabase configuration for storing scraped data');
                return;
            }

            // Prepare data for storage
            $payload = [
                'stat_date' => current_time('Y-m-d'),
                'fil_per_tib' => $data['mining_reward'] ?: 0.0,
                'block_height' => $data['block_height'] ?: null,
                'network_storage_power' => $data['network_storage_power'] ?: null,
                'active_miners' => $data['active_miners'] ?: null,
                'block_reward' => $data['block_reward'] ?: null,
                'fil_production_24h' => $data['fil_production_24h'] ?: null,
                'total_pledge_collateral' => $data['total_pledge_collateral'] ?: null,
                'messages_24h' => $data['messages_24h'] ?: null,
                'sector_initial_pledge' => $data['sector_initial_pledge'] ?: null,
                'latest_block' => $data['latest_block'] ?: null,
                'scraped_at' => current_time('mysql')
            ];

            $response = wp_remote_post($supabase_url . '/rest/v1/network_stats', [
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json',
                    'Prefer' => 'resolution=merge-duplicates'
                ],
                'body' => json_encode($payload),
                'timeout' => 30
            ]);

            if (is_wp_error($response)) {
                error_log('FIL Platform: Error storing scraped data: ' . $response->get_error_message());
                return;
            }

            $status_code = wp_remote_retrieve_response_code($response);
            if ($status_code >= 200 && $status_code < 300) {
            } else {
                error_log('FIL Platform: Failed to store scraped data. Status: ' . $status_code);
            }

        } catch (Exception $e) {
            error_log('FIL Platform: Exception storing scraped data: ' . $e->getMessage());
        }
    }
}

new FIL_Platform_API();

// Add a simple settings page for the admin to enter their Supabase credentials.
function fil_platform_add_settings_menu() {
    add_options_page('FIL Platform Settings', 'FIL Platform Settings', 'manage_options', 'fil-platform-settings', 'fil_platform_options_page');
}
add_action('admin_menu', 'fil_platform_add_settings_menu');

function fil_platform_register_settings() {
    register_setting('fil_platform_options', 'fil_platform_supabase_url');
    register_setting('fil_platform_options', 'fil_platform_supabase_anon_key');
    register_setting('fil_platform_options', 'fil_platform_supabase_service_key');
}
add_action('admin_init', 'fil_platform_register_settings');

function fil_platform_options_page() {
    ?>
    <div class="wrap">
        <h1>FIL Platform Settings</h1>
        <form method="post" action="options.php">
            <?php
            settings_fields('fil_platform_options');
            do_settings_sections('fil_platform_options');
            ?>
            <table class="form-table">
                <tr valign="top">
                    <th scope="row">Supabase URL</th>
                    <td><input type="text" name="fil_platform_supabase_url" value="<?php echo esc_attr(get_option('fil_platform_supabase_url')); ?>" size="100" /></td>
                </tr>
                <tr valign="top">
                    <th scope="row">Supabase Anon Key</th>
                    <td><input type="text" name="fil_platform_supabase_anon_key" value="<?php echo esc_attr(get_option('fil_platform_supabase_anon_key')); ?>" size="100"/></td>
                </tr>
                <tr valign="top">
                    <th scope="row">Supabase Service Key</th>
                    <td>
                        <input type="password" name="fil_platform_supabase_service_key" value="<?php echo esc_attr(get_option('fil_platform_supabase_service_key')); ?>" size="100"/>
                        <p class="description">Service role key for server-side operations (required for scraper). Keep this secure!</p>
                    </td>
                </tr>
            </table>
            <?php submit_button(); ?>
        </form>
    </div>
    <?php
}