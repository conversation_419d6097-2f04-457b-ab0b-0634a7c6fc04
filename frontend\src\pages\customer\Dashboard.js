import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Container, Row, Col, Card, But<PERSON>, Spinner } from 'react-bootstrap';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { useNavigate } from 'react-router-dom';
import { getSupabase } from '../../supabaseClient';

// Helper function to process dashboard data
const processDashboardData = (assets, earnings, orders) => {
    // Calculate available balance (FIL currency)
    const filAsset = assets?.find(asset => asset.currency_code === 'FIL') || {};
    const availableBalance = filAsset.balance_available || 0;

    // Calculate total earnings
    const totalEarnings = earnings?.reduce((sum, earning) => sum + (earning.reward_amount || 0), 0) || 0;

    // Calculate yesterday's earnings
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStart = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
    const yesterdayEnd = new Date(yesterdayStart);
    yesterdayEnd.setDate(yesterdayEnd.getDate() + 1);

    const yesterdayEarnings = earnings?.filter(earning => {
        const earningDate = new Date(earning.created_at);
        return earningDate >= yesterdayStart && earningDate < yesterdayEnd;
    }).reduce((sum, earning) => sum + (earning.reward_amount || 0), 0) || 0;

    // Calculate power pledge (sum of active orders' pledge costs)
    const now = new Date();
    const powerPledge = orders?.filter(order => {
        const startDate = new Date(order.start_at);
        const endDate = new Date(order.end_at);
        return startDate <= now && endDate >= now;
    }).reduce((sum, order) => sum + (order.pledge_cost || 0), 0) || 0;

    // Generate chart data for the last 7 days
    const chartData = [];
    for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        const dateEnd = new Date(dateStart);
        dateEnd.setDate(dateEnd.getDate() + 1);

        const dayEarnings = earnings?.filter(earning => {
            const earningDate = new Date(earning.created_at);
            return earningDate >= dateStart && earningDate < dateEnd;
        }).reduce((sum, earning) => sum + (earning.reward_amount || 0), 0) || 0;

        chartData.push({
            name: `${date.getMonth() + 1}/${date.getDate()}`,
            fil: dayEarnings
        });
    }

    return {
        stats: {
            totalEarnings,
            yesterdayEarnings,
            availableBalance,
            powerPledge
        },
        chartData
    };
};

const StatCard = ({ title, value, subValue, variant, loading }) => (
    <Card className={`bg-${variant} mb-3`}>
        <Card.Body>
            <Card.Title>{title}</Card.Title>
            {loading ? (
                <div className="d-flex align-items-center">
                    <Spinner animation="border" size="sm" className="me-2" />
                    <span>Loading...</span>
                </div>
            ) : (
                <>
                    <h3>{value}</h3>
                    {subValue && <p>{subValue}</p>}
                </>
            )}
        </Card.Body>
    </Card>
);

const CustomerDashboard = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();

    // State management
    const [loading, setLoading] = useState(true);
    const [dashboardData, setDashboardData] = useState({
        totalEarnings: 0,
        yesterdayEarnings: 0,
        availableBalance: 0,
        powerPledge: 0
    });
    const [chartData, setChartData] = useState([]);
    const [error, setError] = useState(null);

    // Fetch dashboard data from Supabase
    useEffect(() => {
        const fetchDashboardData = async () => {
            const supabase = getSupabase();
            if (!supabase) {
                setError('Failed to initialize Supabase client');
                setLoading(false);
                return;
            }

            try {
                setLoading(true);
                const { data: { user } } = await supabase.auth.getUser();

                if (!user) {
                    setError('User not logged in');
                    setLoading(false);
                    return;
                }

                // Fetch user assets (available balance)
                const { data: assets, error: assetsError } = await supabase
                    .from('user_assets')
                    .select('currency_code, balance_available, balance_total')
                    .eq('user_id', user.id);

                if (assetsError) {
                    console.error('Error fetching assets:', assetsError);
                }

                // Fetch earnings data
                const { data: earnings, error: earningsError } = await supabase
                    .from('order_distributions')
                    .select('reward_amount, created_at')
                    .eq('customer_id', user.id)
                    .order('created_at', { ascending: false });

                if (earningsError) {
                    console.error('Error fetching earnings:', earningsError);
                }

                // Fetch orders for power pledge calculation
                const { data: orders, error: ordersError } = await supabase
                    .from('orders')
                    .select('pledge_cost, shares, start_at, end_at')
                    .eq('customer_id', user.id)
                    .eq('review_status', 'approved');

                if (ordersError) {
                    console.error('Error fetching orders:', ordersError);
                }

                // Process the data
                const processedData = processDashboardData(assets, earnings, orders);
                setDashboardData(processedData.stats);
                setChartData(processedData.chartData);

            } catch (error) {
                console.error('Error fetching dashboard data:', error);
                setError('Failed to load dashboard data');
            } finally {
                setLoading(false);
            }
        };

        fetchDashboardData();
    }, []);

    const handleNavigation = (path) => {
        console.log('Navigating to:', path);
        console.log('Current URL:', window.location.href);
        navigate(path);
    };

    // Format number for display
    const formatNumber = (num) => {
        return new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(num || 0);
    };

    if (error) {
        return (
            <Container fluid>
                <Row className="mb-3">
                    <Col>
                        <h2>{t('dashboard')}</h2>
                        <div className="alert alert-danger">{error}</div>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container fluid>
            <Row className="mb-3">
                <Col>
                    <h2>{t('dashboard')}</h2>
                </Col>
            </Row>

            <Row>
                <Col md={3}>
                    <StatCard
                        title={t('total_earnings')}
                        value={`${formatNumber(dashboardData.totalEarnings)} FIL`}
                        variant="primary"
                        loading={loading}
                    />
                </Col>
                <Col md={3}>
                    <StatCard
                        title={t('yesterday_earnings')}
                        value={`${formatNumber(dashboardData.yesterdayEarnings)} FIL`}
                        variant="success"
                        loading={loading}
                    />
                </Col>
                <Col md={3}>
                    <StatCard
                        title={t('available_balance')}
                        value={`${formatNumber(dashboardData.availableBalance)} FIL`}
                        variant="info"
                        loading={loading}
                    />
                </Col>
                <Col md={3}>
                    <StatCard
                        title={t('power_pledge')}
                        value={`${formatNumber(dashboardData.powerPledge)} FIL`}
                        variant="warning"
                        loading={loading}
                    />
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Card.Title>{t('earnings_trend')}</Card.Title>
                            {loading ? (
                                <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
                                    <Spinner animation="border" />
                                    <span className="ms-2">{t('loading')}</span>
                                </div>
                            ) : (
                                <ResponsiveContainer width="95%" height={400}>
                                    <LineChart data={chartData}>
                                        <CartesianGrid strokeDasharray="3 3" />
                                        <XAxis dataKey="name" />
                                        <YAxis yAxisId="left"
                                            width={65}
                                        // label={{ value: 'FIL', angle: -90, position: 'insideLeft' }} 
                                        />
                                        <Legend />
                                        <Line yAxisId="left" type="monotone" dataKey="fil" stroke="#8884d8" name={t('FIL_earnings')} />
                                    </LineChart>
                                </ResponsiveContainer>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row className="mt-4">
                <Col md={6} className="text-center">
                    <Card>
                        <Card.Body>
                            <h4>{t('wallet_management')}</h4>
                            <p>{t('manage_your_digital_assets')}</p>
                            <Button
                                variant="primary"
                                onClick={() => handleNavigation('/my')}
                            >
                                {t('enter_wallet')}
                            </Button>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6} className="text-center">
                    <Card>
                        <Card.Body>
                            <h4>{t('buy_power')}</h4>
                            <p>{t('view_and_purchase_new_power_products')}</p>
                            <Button
                                variant="success"
                                onClick={() => handleNavigation('/products')}
                            >
                                {t('browse_products')}
                            </Button>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

        </Container>
    );
};

export default CustomerDashboard;