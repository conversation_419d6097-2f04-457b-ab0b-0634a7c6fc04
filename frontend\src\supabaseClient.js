
import { createClient } from '@supabase/supabase-js';

// This is a placeholder that will be replaced by the actual config from WordPress
let supabaseUrl = null;
let supabaseAnonKey = null;

let supabase = null;

export const initSupabase = async () => {
    if (supabase) {
        return supabase;
    }

    try {
        // Fetch the config from our WordPress backend
        const response = await fetch(window.wpData.apiUrl + 'config');
        const config = await response.json();

        if (!config.url || !config.anonKey) {
            throw new Error('Supabase config not found.');
        }

        supabaseUrl = config.url;
        supabaseAnonKey = config.anonKey;

        supabase = createClient(supabaseUrl, supabaseAnonKey);
        return supabase;

    } catch (error) {
        console.error("Failed to initialize Supabase client:", error);
        // You might want to show an error message to the user here
        return null;
    }
};

// Export a getter function to ensure Supabase is initialized before use
export const getSupabase = () => {
    if (!supabase) {
        console.error("Supabase has not been initialized. Call initSupabase() first.");
    }
    return supabase;
};

// Helper function to get maker_id for current user
// For maker role: returns current user's id
// For technician role: returns the first maker's id (since there's only one maker per system)
// For other roles: returns null
export const getCurrentMakerId = async () => {
    if (!supabase) {
        console.error("Supabase has not been initialized.");
        return null;
    }

    try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
            console.error("User not authenticated");
            return null;
        }

        // Get user role
        let userRole = user?.user_metadata?.role;
        if (!userRole) {
            const { data, error: roleError } = await supabase
                .from('users')
                .select('role')
                .eq('id', user.id)
                .single();
            if (roleError) {
                console.error('Error fetching user role:', roleError);
                return null;
            }
            userRole = data.role;
        }

        // If user is maker, return their own id
        if (userRole === 'maker') {
            return user.id;
        }

        // If user is technician, get the first maker's id
        if (userRole === 'technician') {
            const { data: makerProfile, error: makerError } = await supabase
                .from('maker_profiles')
                .select('user_id')
                .limit(1)
                .single();

            if (makerError) {
                console.error('Error fetching maker profile for technician:', makerError);
                return null;
            }

            return makerProfile.user_id;
        }

        // For other roles, return null
        return null;
    } catch (error) {
        console.error('Error in getCurrentMakerId:', error);
        return null;
    }
};
