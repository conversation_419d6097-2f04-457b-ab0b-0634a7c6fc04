import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, Button, Form, Pagination } from 'react-bootstrap';
import { FaDownload } from 'react-icons/fa';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const NetworkStats = () => {
    const { t } = useTranslation();
    const [stats, setStats] = useState([]);
    const [loading, setLoading] = useState(true);

    // Pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [statsPerPage] = useState(10);
    const [paginatedStats, setPaginatedStats] = useState([]);

    useEffect(() => {
        const fetchStats = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch network stats
            const { data, error } = await supabase
                .from('network_stats')
                .select(`
                    stat_date,
                    fil_per_tib
                `)
                .order('stat_date', { ascending: false });

            if (error) {
                console.error('Error fetching network stats:', error);
            } else {
                setStats(data);
            }
            setLoading(false);
        };

        fetchStats();
    }, []);

    // Paginate stats
    useEffect(() => {
        const indexOfLastStat = currentPage * statsPerPage;
        const indexOfFirstStat = indexOfLastStat - statsPerPage;
        const currentStats = stats.slice(indexOfFirstStat, indexOfLastStat);
        setPaginatedStats(currentStats);
    }, [stats, currentPage, statsPerPage]);

    // Pagination handlers
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const totalPages = Math.ceil(stats.length / statsPerPage);

    // Export stats to CSV
    const exportToCSV = () => {
        if (stats.length === 0) {
            alert(t('no_data_to_export'));
            return;
        }

        // Define CSV headers
        const headers = [
            t('stat_date'),
            t('fil_per_tib')
        ];

        // Convert data to CSV format
        const csvData = stats.map(stat => [
            new Date(stat.stat_date).toLocaleDateString(),
            stat.fil_per_tib ? Number(stat.fil_per_tib).toFixed(8) : '0'
        ]);

        // Combine headers and data
        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Add UTF-8 BOM to ensure proper encoding for Japanese characters
        const BOM = '\uFEFF';
        const csvWithBOM = BOM + csvContent;

        // Create and download CSV file with proper UTF-8 encoding
        const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `maker_network_stats_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    if (loading) {
        return <div>{t('loading_network_stats')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('network_stats')}</h2>

            {/* Top Operation Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('export_data')}</Form.Label>
                                        <div>
                                            <Button
                                                variant="success"
                                                onClick={exportToCSV}
                                                className="mb-2"
                                                disabled={stats.length === 0}
                                            >
                                                <FaDownload className="me-1" />
                                                {t('export_all')}
                                            </Button>
                                        </div>
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('stat_date')}</th>
                                        <th>{t('fil_per_tib')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedStats.length === 0 ? (
                                        <tr>
                                            <td colSpan="2" className="text-center">{t('no_network_stats_available')}</td>
                                        </tr>
                                    ) : (
                                        paginatedStats.map((stat, index) => (
                                            <tr key={`${stat.stat_date}`}>
                                                <td>{new Date(stat.stat_date).toLocaleDateString()}</td>
                                                <td>{stat.fil_per_tib ? Number(stat.fil_per_tib).toFixed(8) : '0'} FIL/TiB</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="d-flex justify-content-center">
                                    <Pagination>
                                        <Pagination.First
                                            onClick={() => handlePageChange(1)}
                                            disabled={currentPage === 1}
                                        />
                                        <Pagination.Prev
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                        />

                                        {/* Show page numbers */}
                                        {[...Array(totalPages)].map((_, index) => {
                                            const pageNumber = index + 1;
                                            // Show first page, last page, current page, and pages around current page
                                            if (
                                                pageNumber === 1 ||
                                                pageNumber === totalPages ||
                                                (pageNumber >= currentPage - 2 && pageNumber <= currentPage + 2)
                                            ) {
                                                return (
                                                    <Pagination.Item
                                                        key={pageNumber}
                                                        active={pageNumber === currentPage}
                                                        onClick={() => handlePageChange(pageNumber)}
                                                    >
                                                        {pageNumber}
                                                    </Pagination.Item>
                                                );
                                            } else if (
                                                pageNumber === currentPage - 3 ||
                                                pageNumber === currentPage + 3
                                            ) {
                                                return <Pagination.Ellipsis key={pageNumber} />;
                                            }
                                            return null;
                                        })}

                                        <Pagination.Next
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                        />
                                        <Pagination.Last
                                            onClick={() => handlePageChange(totalPages)}
                                            disabled={currentPage === totalPages}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default NetworkStats;