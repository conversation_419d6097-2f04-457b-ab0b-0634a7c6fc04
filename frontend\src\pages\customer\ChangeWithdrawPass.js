import React, { useState } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { getSupabase } from '../../supabaseClient';
import { useNavigate } from 'react-router-dom';
import bcrypt from 'bcryptjs';
import { FaEye, FaEyeSlash } from 'react-icons/fa';

const ChangeWithdrawPass = () => {
    const { t } = useTranslation();
    const [currentPassword, setCurrentPassword] = useState('');
    const [newWithdrawPassword, setNewWithdrawPassword] = useState('');
    const [confirmWithdrawPassword, setConfirmWithdrawPassword] = useState('');
    const [loading, setLoading] = useState(false);
    const [message, setMessage] = useState('');
    const [error, setError] = useState('');
    const [showCurrentPassword, setShowCurrentPassword] = useState(false);
    const [showNewWithdrawPassword, setShowNewWithdrawPassword] = useState(false);
    const [showConfirmWithdrawPassword, setShowConfirmWithdrawPassword] = useState(false);
    const navigate = useNavigate();

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setMessage('');

        // 验证新提现密码和确认密码是否一致
        if (newWithdrawPassword !== confirmWithdrawPassword) {
            setError(t('passwords_do_not_match'));
            return;
        }

        // 验证新提现密码长度
        if (newWithdrawPassword.length < 6) {
            setError(t('password_too_short'));
            return;
        }

        setLoading(true);

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error(t('backend_connection_failed'));
            }

            // 获取当前用户
            const { data: { user }, error: userError } = await supabase.auth.getUser();
            if (userError || !user) {
                throw new Error(t('user_not_authenticated'));
            }

            // 首先验证当前登录密码是否正确
            // 通过重新登录来验证当前密码
            const { error: signInError } = await supabase.auth.signInWithPassword({
                email: user.email,
                password: currentPassword,
            });

            if (signInError) {
                throw new Error(t('current_password_incorrect'));
            }

            // 对新提现密码进行哈希处理
            const saltRounds = 10;
            const hashedWithdrawPassword = await bcrypt.hash(newWithdrawPassword, saltRounds);

            // 更新customer_profiles表中的withdraw_pwd_hash字段
            const { data: updateData, error: updateError } = await supabase
                .from('customer_profiles')
                .update({ withdraw_pwd_hash: hashedWithdrawPassword })
                .eq('user_id', user.id)
                .select();

            if (updateError) {
                console.error('Database update error:', updateError);
                throw updateError;
            }

            // 检查是否有数据被更新
            if (!updateData || updateData.length === 0) {
                console.warn('No rows were updated. User profile may not exist.');
                // 尝试创建customer_profiles记录
                const { data: insertData, error: insertError } = await supabase
                    .from('customer_profiles')
                    .insert({
                        user_id: user.id,
                        withdraw_pwd_hash: hashedWithdrawPassword,
                        verify_status: 'not_submitted'
                    })
                    .select();

                if (insertError) {
                    console.error('Database insert error:', insertError);
                    throw new Error('Failed to create or update withdraw password: ' + insertError.message);
                }
            }

            setMessage(t('withdraw_password_updated_successfully'));

            // 清空表单
            setCurrentPassword('');
            setNewWithdrawPassword('');
            setConfirmWithdrawPassword('');

            // 3秒后跳转回客户页面
            setTimeout(() => {
                navigate('/customer');
            }, 3000);

        } catch (err) {
            console.error('Withdraw password update error:', err);
            setError(err.message || t('withdraw_password_update_failed'));
        }

        setLoading(false);
    };

    return (
        <Container>
            <Row className="justify-content-center">
                <Col md={6}>
                    <Card>
                        <Card.Header>
                            <h4 className="mb-0">{t('change_withdraw_password')}</h4>
                        </Card.Header>
                        <Card.Body>
                            {error && <Alert variant="danger">{error}</Alert>}
                            {message && <Alert variant="success">{message}</Alert>}

                            <Form onSubmit={handleSubmit}>
                                <Form.Group className="mb-3">
                                    <Form.Label><span className="text-danger">*</span>{t('current_login_password')}</Form.Label>
                                    <div style={{ position: 'relative' }}>
                                        <Form.Control
                                            type={showCurrentPassword ? "text" : "password"}
                                            value={currentPassword}
                                            onChange={(e) => setCurrentPassword(e.target.value)}
                                            required
                                            placeholder={t('enter_current_password')}
                                            style={{ paddingRight: '40px' }}
                                        />
                                        <span
                                            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                            style={{
                                                position: 'absolute',
                                                right: '12px',
                                                top: '50%',
                                                transform: 'translateY(-50%)',
                                                cursor: 'pointer',
                                                color: '#6c757d',
                                                fontSize: '16px'
                                            }}
                                        >
                                            {showCurrentPassword ? <FaEyeSlash /> : <FaEye />}
                                        </span>
                                    </div>
                                    <Form.Text className="text-muted">
                                        {t('enter_login_password_to_verify')}
                                    </Form.Text>
                                </Form.Group>

                                <Form.Group className="mb-3">
                                    <Form.Label><span className="text-danger">*</span>{t('new_password')}</Form.Label>
                                    <div style={{ position: 'relative' }}>
                                        <Form.Control
                                            type={showNewWithdrawPassword ? "text" : "password"}
                                            value={newWithdrawPassword}
                                            onChange={(e) => setNewWithdrawPassword(e.target.value)}
                                            required
                                            minLength={6}
                                            placeholder={t('enter_new_withdraw_password')}
                                            style={{ paddingRight: '40px' }}
                                        />
                                        <span
                                            onClick={() => setShowNewWithdrawPassword(!showNewWithdrawPassword)}
                                            style={{
                                                position: 'absolute',
                                                right: '12px',
                                                top: '50%',
                                                transform: 'translateY(-50%)',
                                                cursor: 'pointer',
                                                color: '#6c757d',
                                                fontSize: '16px'
                                            }}
                                        >
                                            {showNewWithdrawPassword ? <FaEyeSlash /> : <FaEye />}
                                        </span>
                                    </div>
                                    <Form.Text className="text-muted">
                                        {t('withdraw_password_requirements')}
                                    </Form.Text>
                                </Form.Group>

                                <Form.Group className="mb-3">
                                    <Form.Label><span className="text-danger">*</span>{t('confirm_new_password')}</Form.Label>
                                    <div style={{ position: 'relative' }}>
                                        <Form.Control
                                            type={showConfirmWithdrawPassword ? "text" : "password"}
                                            value={confirmWithdrawPassword}
                                            onChange={(e) => setConfirmWithdrawPassword(e.target.value)}
                                            required
                                            minLength={6}
                                            placeholder={t('confirm_new_withdraw_password')}
                                            style={{ paddingRight: '40px' }}
                                        />
                                        <span
                                            onClick={() => setShowConfirmWithdrawPassword(!showConfirmWithdrawPassword)}
                                            style={{
                                                position: 'absolute',
                                                right: '12px',
                                                top: '50%',
                                                transform: 'translateY(-50%)',
                                                cursor: 'pointer',
                                                color: '#6c757d',
                                                fontSize: '16px'
                                            }}
                                        >
                                            {showConfirmWithdrawPassword ? <FaEyeSlash /> : <FaEye />}
                                        </span>
                                    </div>
                                </Form.Group>

                                <div className="d-grid gap-2">
                                    <Button
                                        variant="primary"
                                        type="submit"
                                        disabled={loading}
                                    >
                                        {loading ? t('updating') : t('update_withdraw_password')}
                                    </Button>
                                    <Button
                                        variant="secondary"
                                        onClick={() => navigate('/customer')}
                                        disabled={loading}
                                    >
                                        {t('cancel')}
                                    </Button>
                                </div>
                            </Form>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default ChangeWithdrawPass;
