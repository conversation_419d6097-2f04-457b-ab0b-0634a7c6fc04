import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, Form, InputGroup, Pagination, Button } from 'react-bootstrap';
import { FaDownload } from 'react-icons/fa';
import { getSupabase, getCurrentMakerId } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const CustomerAssets = () => {
    const { t } = useTranslation();
    const [assets, setAssets] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [filteredAssets, setFilteredAssets] = useState([]);

    // Pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [assetsPerPage] = useState(10);
    const [paginatedAssets, setPaginatedAssets] = useState([]);

    useEffect(() => {
        const fetchAssets = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch user assets with user information
            const { data, error } = await supabase
                .from('user_assets')
                .select(`
                    user_id,
                    currency_code,
                    balance_available,
                    balance_locked,
                    balance_total,
                    withdrawn_total,
                    users (
                        email,
                        phone,
                        customer_profiles (
                            agent_profiles (
                                users (
                                    email
                                )
                            )
                        )
                    ),
                    currencies:currencies!user_assets_currency_code_fkey (
                        code,
                        total_supply,
                        withdrawable
                    )
                `)
                .order('balance_total', { ascending: false });

            if (error) {
                console.error('Error fetching customer assets:', error);
            } else {
                setAssets(data);
            }
            setLoading(false);
        };

        fetchAssets();
    }, []);

    // Filter assets based on search criteria
    useEffect(() => {
        let filtered = assets;

        // Search by customer email or agent email
        if (searchTerm) {
            filtered = filtered.filter(asset =>
                asset.users?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                asset.users?.customer_profiles?.agent_profiles?.users?.email?.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        setFilteredAssets(filtered);
        setCurrentPage(1); // Reset to first page when filters change
    }, [assets, searchTerm, startDate, endDate]);

    // Paginate filtered assets
    useEffect(() => {
        const indexOfLastAsset = currentPage * assetsPerPage;
        const indexOfFirstAsset = indexOfLastAsset - assetsPerPage;
        const currentAssets = filteredAssets.slice(indexOfFirstAsset, indexOfLastAsset);
        setPaginatedAssets(currentAssets);
    }, [filteredAssets, currentPage, assetsPerPage]);

    // Pagination handlers
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const totalPages = Math.ceil(filteredAssets.length / assetsPerPage);

    // Export filtered assets to CSV
    const exportToCSV = () => {
        if (filteredAssets.length === 0) {
            alert(t('no_data_to_export'));
            return;
        }

        // Define CSV headers
        const headers = [
            t('user_email'),
            t('agent'),
            t('currency_code'),
            t('available_balance'),
            t('locked_balance'),
            t('total_balance'),
            t('withdrawn_total')
        ];

        // Convert data to CSV format
        const csvData = filteredAssets.map(asset => [
            asset.users?.email || '-',
            asset.users?.customer_profiles?.agent_profiles?.users?.email || '-',
            asset.currency_code || '-',
            asset.balance_available?.toFixed(6) || '0.000000',
            asset.balance_locked?.toFixed(6) || '0.000000',
            asset.balance_total?.toFixed(6) || '0.000000',
            asset.withdrawn_total?.toFixed(6) || '0.000000'
        ]);

        // Combine headers and data
        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Add UTF-8 BOM to ensure proper encoding for Japanese characters
        const BOM = '\uFEFF';
        const csvWithBOM = BOM + csvContent;

        // Create and download CSV file with proper UTF-8 encoding
        const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `customer_assets_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    if (loading) {
        return <div>{t('loading_customer_assets')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('customer_assets')}</h2>

            {/* Search and Filter Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('export_data')}</Form.Label>
                                        <div>
                                            <Button
                                                variant="success"
                                                onClick={exportToCSV}
                                                className="mb-2"
                                                disabled={filteredAssets.length === 0}
                                            >
                                                <FaDownload className="me-1" />
                                                {t('export_all')}
                                            </Button>
                                        </div>
                                    </Form.Group>
                                </Col>
                                <Col md={4}>
                                    <Form.Group>
                                        <Form.Label>{t('search_customer')}</Form.Label>
                                        <InputGroup>
                                            <Form.Control
                                                type="text"
                                                placeholder={t('search_customer_or_agent_email')}
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                            />
                                        </InputGroup>
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Assets Table */}
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('user_email')}</th>
                                        <th>{t('agent')}</th>
                                        <th>{t('currency_code')}</th>
                                        <th>{t('available_balance')}</th>
                                        <th>{t('locked_balance')}</th>
                                        <th>{t('total_balance')}</th>
                                        <th>{t('withdrawn_total')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedAssets.length === 0 ? (
                                        <tr>
                                            <td colSpan="7" className="text-center">{t('no_customer_assets_available')}</td>
                                        </tr>
                                    ) : (
                                        paginatedAssets.map((asset, index) => (
                                            <tr key={`${asset.user_id}-${asset.currency_code}`}>
                                                <td>{asset.users?.email || '-'}</td>
                                                <td>{asset.users?.customer_profiles?.agent_profiles?.users?.email || '-'}</td>
                                                <td>{asset.currency_code}</td>
                                                <td>{asset.balance_available?.toFixed(6) || '0.000000'}</td>
                                                <td>{asset.balance_locked?.toFixed(6) || '0.000000'}</td>
                                                <td>{asset.balance_total?.toFixed(6) || '0.000000'}</td>
                                                <td>{asset.withdrawn_total?.toFixed(6) || '0.000000'}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="d-flex justify-content-center">
                                    <Pagination>
                                        <Pagination.First
                                            onClick={() => handlePageChange(1)}
                                            disabled={currentPage === 1}
                                        />
                                        <Pagination.Prev
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                        />

                                        {/* Show page numbers */}
                                        {[...Array(totalPages)].map((_, index) => {
                                            const pageNumber = index + 1;
                                            // Show first page, last page, current page, and pages around current page
                                            if (
                                                pageNumber === 1 ||
                                                pageNumber === totalPages ||
                                                (pageNumber >= currentPage - 2 && pageNumber <= currentPage + 2)
                                            ) {
                                                return (
                                                    <Pagination.Item
                                                        key={pageNumber}
                                                        active={pageNumber === currentPage}
                                                        onClick={() => handlePageChange(pageNumber)}
                                                    >
                                                        {pageNumber}
                                                    </Pagination.Item>
                                                );
                                            } else if (
                                                pageNumber === currentPage - 3 ||
                                                pageNumber === currentPage + 3
                                            ) {
                                                return <Pagination.Ellipsis key={pageNumber} />;
                                            }
                                            return null;
                                        })}

                                        <Pagination.Next
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                        />
                                        <Pagination.Last
                                            onClick={() => handlePageChange(totalPages)}
                                            disabled={currentPage === totalPages}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default CustomerAssets;
