<?php
// Exit if accessed directly.
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Filfox Data Scraper for WordPress
 * 
 * This class handles scraping data from filfox.info and storing it in Supabase
 */
class FIL_Platform_Filfox_Scraper
{

    public function __construct()
    {
        // Check if cron is scheduled, if not schedule it once
        add_action('init', [$this, 'ensure_cron_scheduled']);
        add_action('fil_platform_filfox_scraper', [$this, 'run_filfox_scraper']);

        // Add miner earnings cron
        add_action('init', [$this, 'ensure_miner_earnings_cron_scheduled']);
        add_action('fil_platform_miner_earnings_scraper', [$this, 'run_miner_earnings_scraper']);

        // Add admin hooks
        add_action('wp_ajax_fil_platform_manual_scrape', [$this, 'ajax_manual_scrape']);
        add_action('wp_ajax_fil_platform_scraper_status', [$this, 'ajax_scraper_status']);
        add_action('wp_ajax_fil_platform_reschedule_cron', [$this, 'ajax_reschedule_cron']);
        add_action('wp_ajax_fil_platform_manual_miner_scrape', [$this, 'ajax_manual_miner_scrape']);
    }

    /**
     * Ensure cron is scheduled (only schedule if not already scheduled)
     */
    public function ensure_cron_scheduled()
    {
        $existing_timestamp = wp_next_scheduled('fil_platform_filfox_scraper');
        if (!$existing_timestamp) {
            error_log('FIL Platform: No cron scheduled, scheduling now...');
            $this->schedule_filfox_cron();
        }
    }

    /**
     * Ensure miner earnings cron is scheduled
     */
    public function ensure_miner_earnings_cron_scheduled()
    {
        $existing_timestamp = wp_next_scheduled('fil_platform_miner_earnings_scraper');
        if (!$existing_timestamp) {
            error_log('FIL Platform: No miner earnings cron scheduled, scheduling now...');
            $this->schedule_miner_earnings_cron();
        }
    }

    /**
     * Schedule the filfox scraper cron job
     */
    public function schedule_filfox_cron()
    {
        // Check if already properly scheduled for today's 2 AM JST
        $existing_timestamp = wp_next_scheduled('fil_platform_filfox_scraper');

        if ($existing_timestamp) {
            // Calculate what today's 2 AM JST should be in UTC
            $jst_offset = 9 * 3600;
            $current_jst = time() + $jst_offset;
            $today_jst = strtotime(date('Y-m-d', $current_jst));
            $target_jst = $today_jst + (2 * 3600);

            if ($target_jst <= $current_jst) {
                $target_jst += 24 * 3600;
            }

            $expected_utc = $target_jst - $jst_offset;

            // If already scheduled for the correct time, don't reschedule
            if (abs($existing_timestamp - $expected_utc) < 3600) { // Within 1 hour tolerance
                error_log('FIL Platform: Cron already properly scheduled for ' . date('Y-m-d H:i:s', $existing_timestamp) . ' UTC');
                return;
            }

            // Clear existing schedule if it's for wrong time
            wp_unschedule_event($existing_timestamp, 'fil_platform_filfox_scraper');
            error_log('FIL Platform: Cleared existing cron schedule (wrong time)');

            // Verify it was actually cleared
            $still_exists = wp_next_scheduled('fil_platform_filfox_scraper');
            if ($still_exists) {
                error_log('FIL Platform: Warning - cron event still exists after clearing');
                wp_clear_scheduled_hook('fil_platform_filfox_scraper');
            }
        }

        // Calculate 2:00 AM JST in UTC
        // JST is UTC+9, so 2:00 AM JST = 17:00 UTC (previous day)
        $jst_offset = 9 * 3600; // 9 hours in seconds

        // Get current time in JST
        $current_jst = time() + $jst_offset;
        $today_jst = strtotime(date('Y-m-d', $current_jst));
        $target_jst = $today_jst + (2 * 3600); // 2:00 AM JST today

        // If 2:00 AM JST has already passed today, schedule for tomorrow
        if ($target_jst <= $current_jst) {
            $target_jst += 24 * 3600; // Add 24 hours
        }

        // Convert back to UTC for WordPress cron
        $timestamp_utc = $target_jst - $jst_offset;

        error_log('FIL Platform: Attempting to schedule cron for timestamp: ' . $timestamp_utc . ' (UTC: ' . date('Y-m-d H:i:s', $timestamp_utc) . ')');
        error_log('FIL Platform: Current time: ' . time() . ' (UTC: ' . date('Y-m-d H:i:s', time()) . ')');

        // Schedule the event
        $result = wp_schedule_event($timestamp_utc, 'daily', 'fil_platform_filfox_scraper');

        if ($result === false) {
            error_log('FIL Platform: Failed to schedule cron job');
            error_log('FIL Platform: Checking if timestamp is in the past: ' . ($timestamp_utc <= time() ? 'YES' : 'NO'));

            // Try to get more info about why it failed
            $existing = wp_next_scheduled('fil_platform_filfox_scraper');
            error_log('FIL Platform: Existing scheduled event: ' . ($existing ? date('Y-m-d H:i:s', $existing) : 'NONE'));

            // Try one more time after a brief pause
            error_log('FIL Platform: Retrying cron schedule...');
            wp_clear_scheduled_hook('fil_platform_filfox_scraper');
            $retry_result = wp_schedule_event($timestamp_utc, 'daily', 'fil_platform_filfox_scraper');

            if ($retry_result !== false) {
                error_log('FIL Platform: Retry successful - Filfox scraper scheduled for:');
                error_log('  UTC: ' . date('Y-m-d H:i:s', $timestamp_utc));
                error_log('  JST: ' . date('Y-m-d H:i:s', $timestamp_utc + $jst_offset));
                error_log('  Next run in: ' . $this->human_time_diff($timestamp_utc, time()));
            } else {
                error_log('FIL Platform: Retry also failed');
            }
        } else {
            // Log the scheduling with both UTC and JST times
            error_log('FIL Platform: Filfox scraper scheduled for:');
            error_log('  UTC: ' . date('Y-m-d H:i:s', $timestamp_utc));
            error_log('  JST: ' . date('Y-m-d H:i:s', $timestamp_utc + $jst_offset));
            error_log('  Next run in: ' . $this->human_time_diff($timestamp_utc, time()));
        }

        // Store scheduling info for debugging
        update_option('fil_platform_cron_scheduled_at', current_time('mysql'));
        update_option('fil_platform_cron_next_run_utc', $timestamp_utc);
    }

    /**
     * Schedule the miner earnings scraper cron job for 9:00 AM JST daily
     */
    public function schedule_miner_earnings_cron()
    {
        // Clear any existing cron job first
        wp_clear_scheduled_hook('fil_platform_miner_earnings_scraper');

        // JST is UTC+9
        $jst_offset = 9 * 3600; // 9 hours in seconds

        // Get current time in JST
        $current_jst = time() + $jst_offset;
        $today_jst = strtotime(date('Y-m-d', $current_jst));
        $target_jst = $today_jst + (9 * 3600); // 9:00 AM JST today

        // If 9:00 AM JST has already passed today, schedule for tomorrow
        if ($target_jst <= $current_jst) {
            $target_jst += 24 * 3600; // Add 24 hours
        }

        // Convert back to UTC for WordPress cron
        $timestamp_utc = $target_jst - $jst_offset;

        error_log('FIL Platform: Attempting to schedule miner earnings cron for timestamp: ' . $timestamp_utc . ' (UTC: ' . date('Y-m-d H:i:s', $timestamp_utc) . ')');

        // Schedule the event
        $result = wp_schedule_event($timestamp_utc, 'daily', 'fil_platform_miner_earnings_scraper');

        if ($result === false) {
            error_log('FIL Platform: Failed to schedule miner earnings cron job');
            // Try one more time after clearing
            wp_clear_scheduled_hook('fil_platform_miner_earnings_scraper');
            $retry_result = wp_schedule_event($timestamp_utc, 'daily', 'fil_platform_miner_earnings_scraper');

            if ($retry_result !== false) {
                error_log('FIL Platform: Retry successful - Miner earnings scraper scheduled for:');
                error_log('  UTC: ' . date('Y-m-d H:i:s', $timestamp_utc));
                error_log('  JST: ' . date('Y-m-d H:i:s', $timestamp_utc + $jst_offset));
            } else {
                error_log('FIL Platform: Retry also failed for miner earnings cron');
            }
        } else {
            error_log('FIL Platform: Miner earnings scraper scheduled for:');
            error_log('  UTC: ' . date('Y-m-d H:i:s', $timestamp_utc));
            error_log('  JST: ' . date('Y-m-d H:i:s', $timestamp_utc + $jst_offset));
        }

        // Store scheduling info for debugging
        update_option('fil_platform_miner_cron_scheduled_at', current_time('mysql'));
        update_option('fil_platform_miner_cron_next_run_utc', $timestamp_utc);
    }

    /**
     * Run the filfox scraper
     */
    public function run_filfox_scraper()
    {
        error_log('FIL Platform: Starting filfox scraper via CRON...');

        try {
            $result = $this->scrape_filfox_data();

            if ($result['success']) {
                error_log('FIL Platform: Filfox scraper completed successfully via CRON');

                $this->store_network_stats($result['data']);
                error_log('FIL Platform: Network stats stored successfully via CRON');
            } else {
                error_log('FIL Platform: Filfox scraper failed via CRON: ' . $result['error']);
            }

        } catch (Exception $e) {
            error_log('FIL Platform: Filfox scraper exception via CRON: ' . $e->getMessage());
        }

        // Update last run time regardless of success/failure
        update_option('fil_platform_last_cron_run', current_time('mysql'));
    }

    /**
     * Run the miner earnings scraper
     */
    public function run_miner_earnings_scraper()
    {
        error_log('FIL Platform: Starting miner earnings scraper via CRON...');

        try {
            $result = $this->scrape_miner_earnings_data();

            if ($result['success']) {
                error_log('FIL Platform: Miner earnings scraper completed successfully via CRON');

                $this->store_miner_earnings($result['data']);
                error_log('FIL Platform: Miner earnings stored successfully via CRON');
            } else {
                error_log('FIL Platform: Miner earnings scraper failed via CRON: ' . $result['error']);
            }

        } catch (Exception $e) {
            error_log('FIL Platform: Miner earnings scraper exception via CRON: ' . $e->getMessage());
        }

        // Update last run time regardless of success/failure
        update_option('fil_platform_last_miner_cron_run', current_time('mysql'));
    }

    /**
     * AJAX handler for manual scrape
     */
    public function ajax_manual_scrape()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        check_ajax_referer('fil_platform_scraper_nonce', 'nonce');

        try {
            $result = $this->scrape_filfox_data();

            if ($result['success']) {
                $this->store_network_stats($result['data']);
                wp_send_json_success([
                    'message' => 'Filfox data scraped and stored successfully',
                    'data' => $result['data']
                ]);
            } else {
                wp_send_json_error([
                    'message' => 'Failed to scrape filfox data',
                    'error' => $result['error']
                ]);
            }

        } catch (Exception $e) {
            wp_send_json_error([
                'message' => 'Exception occurred',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * AJAX handler for scraper status
     */
    public function ajax_scraper_status()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        check_ajax_referer('fil_platform_scraper_nonce', 'nonce');

        $next_scheduled = wp_next_scheduled('fil_platform_filfox_scraper');
        $last_run = get_option('fil_platform_last_scrape_run', 'Never');
        $last_success = get_option('fil_platform_last_scrape_success', 'Never');

        wp_send_json_success([
            'next_scheduled' => $next_scheduled ? date('Y-m-d H:i:s', $next_scheduled) . ' UTC' : 'Not scheduled',
            'next_scheduled_jst' => $next_scheduled ? date('Y-m-d H:i:s', $next_scheduled + 9 * 3600) . ' JST' : 'Not scheduled',
            'last_run' => $last_run,
            'last_success' => $last_success,
            'cron_enabled' => !defined('DISABLE_WP_CRON') || !DISABLE_WP_CRON
        ]);
    }

    /**
     * AJAX handler for rescheduling cron
     */
    public function ajax_reschedule_cron()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        check_ajax_referer('fil_platform_scraper_nonce', 'nonce');

        try {
            // Clear existing schedule
            wp_clear_scheduled_hook('fil_platform_filfox_scraper');

            // Reschedule
            $this->schedule_filfox_cron();

            // Get new schedule info
            $next_scheduled = wp_next_scheduled('fil_platform_filfox_scraper');

            wp_send_json_success([
                'message' => 'Cron job rescheduled successfully',
                'next_scheduled' => $next_scheduled ? date('Y-m-d H:i:s', $next_scheduled) . ' UTC' : 'Not scheduled',
                'next_scheduled_jst' => $next_scheduled ? date('Y-m-d H:i:s', $next_scheduled + 9 * 3600) . ' JST' : 'Not scheduled'
            ]);

        } catch (Exception $e) {
            wp_send_json_error([
                'message' => 'Failed to reschedule cron job',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Scrape data from filfox.info using WordPress HTTP API
     */
    private function scrape_filfox_data()
    {
        update_option('fil_platform_last_scrape_run', current_time('mysql'));

        try {
            // Use WordPress HTTP API to fetch the page
            $response = wp_remote_get('https://filfox.info/en', [
                'timeout' => 30,
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'headers' => [
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language' => 'en-US,en;q=0.5',
                    'Accept-Encoding' => 'gzip, deflate',
                    'Connection' => 'keep-alive',
                ]
            ]);

            if (is_wp_error($response)) {
                throw new Exception('HTTP request failed: ' . $response->get_error_message());
            }

            $body = wp_remote_retrieve_body($response);
            $status_code = wp_remote_retrieve_response_code($response);

            if ($status_code !== 200) {
                throw new Exception('HTTP request returned status code: ' . $status_code);
            }

            if (empty($body)) {
                throw new Exception('Empty response body');
            }

            // Parse the HTML to extract data
            $data = $this->parse_filfox_html($body);

            if (!$data['mining_reward']) {
                throw new Exception('No valid data found in the response');
            }

            update_option('fil_platform_last_scrape_success', current_time('mysql'));

            return [
                'success' => true,
                'data' => $data
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Parse HTML content to extract and mining reward
     */
    private function parse_filfox_html($html)
    {
        $mining_reward = null;

        // Remove script and style tags to avoid false matches
        $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);
        $html = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $html);

        // Try to find mining reward with more specific patterns
        // Pattern 1: "24h Average Mining Reward" followed by number and "FIL/TiB"
        if (preg_match('/24h\s+Average\s+Mining\s+Reward[^0-9]*([0-9]+\.?[0-9]*)\s*FIL\/TiB/i', $html, $matches)) {
            $reward = (float) $matches[1];
            if ($reward > 0 && $reward < 10) { // Reasonable range for FIL/TiB
                $mining_reward = $reward;
            }
        }

        // Pattern 2: Look for decimal numbers followed by "FIL/TiB"
        if (!$mining_reward && preg_match('/([0-9]*\.?[0-9]+)\s*FIL\/TiB/i', $html, $matches)) {
            $reward = (float) $matches[1];
            if ($reward > 0 && $reward < 10) {
                $mining_reward = $reward;
            }
        }

        // Pattern 3: More general FIL pattern but with stricter validation
        if (!$mining_reward && preg_match('/([0-9]*\.?[0-9]+)\s*FIL(?!\/)/i', $html, $matches)) {
            $reward = (float) $matches[1];
            if ($reward > 0 && $reward < 1) { // Very small numbers are more likely to be per TiB rewards
                $mining_reward = $reward;
            }
        }

        // If still no data found, try to extract from JSON-LD or other structured data
        if ((!$mining_reward) && preg_match('/<script[^>]*type=["\']application\/ld\+json["\'][^>]*>(.*?)<\/script>/is', $html, $matches)) {
            $json_data = json_decode($matches[1], true);
            if ($json_data) {
                // Try to extract from structured data
                $this->extract_from_structured_data($json_data, $mining_reward);
            }
        }

        // Final debug output
        error_log("FIL Platform: Final results -  Mining Reward: " . ($mining_reward ?: 'NOT FOUND'));

        return [
            'mining_reward' => $mining_reward ?: 0.0,
            'scraped_at' => current_time('mysql')
        ];
    }

    /**
     * Extract data from structured JSON data
     */
    private function extract_from_structured_data($data, &$mining_reward)
    {
        // Recursively search through structured data
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                if (is_string($key)) {
                    if (stripos($key, 'reward') !== false && is_numeric($value)) {
                        $reward = (float) $value;
                        if ($reward > 0 && $reward < 1000) {
                            $mining_reward = $reward;
                        }
                    }
                }
                if (is_array($value)) {
                    $this->extract_from_structured_data($value, $mining_reward);
                }
            }
        }
    }

    /**
     * Store network stats in Supabase
     */
    private function store_network_stats($data)
    {
        error_log('FIL Platform: Starting store_network_stats...');

        $supabase_url = get_option('fil_platform_supabase_url');
        $supabase_service_key = get_option('fil_platform_supabase_service_key');

        if (!$supabase_service_key) {
            // Fallback to anon key if service key not available
            $supabase_service_key = get_option('fil_platform_supabase_anon_key');
            error_log('FIL Platform: Using anon key fallback: ' . ($supabase_service_key ? 'configured' : 'NOT configured'));
        }

        if (!$supabase_url || !$supabase_service_key) {
            error_log('FIL Platform: Missing Supabase configuration - URL: ' . ($supabase_url ? 'OK' : 'MISSING') . ', Key: ' . ($supabase_service_key ? 'OK' : 'MISSING'));
            throw new Exception('Supabase configuration not found');
        }

        $payload = [
            'fil_per_tib' => $data['mining_reward']
        ];

        $response = wp_remote_post($supabase_url . '/rest/v1/network_stats', [
            'headers' => [
                'apikey' => $supabase_service_key,
                'Authorization' => 'Bearer ' . $supabase_service_key,
                'Content-Type' => 'application/json',
                'Prefer' => 'resolution=merge-duplicates'
            ],
            'body' => json_encode($payload),
            'timeout' => 30
        ]);

        if (is_wp_error($response)) {
            error_log('FIL Platform: WP Error: ' . $response->get_error_message());
            throw new Exception('Failed to store data: ' . $response->get_error_message());
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($status_code < 200 || $status_code >= 300) {
            throw new Exception('Supabase API error (HTTP ' . $status_code . '): ' . $response_body);
        }

        error_log('FIL Platform: Network stats stored successfully');
    }

    /**
     * Helper function to format time differences
     */
    private function human_time_diff($from, $to = 0)
    {
        if (empty($to)) {
            $to = time();
        }

        $diff = (int) abs($to - $from);

        if ($diff < 3600) { // Less than 1 hour
            $mins = round($diff / 60);
            if ($mins <= 1) {
                $mins = 1;
            }
            return $mins . ' minute' . ($mins > 1 ? 's' : '');
        } elseif ($diff < 86400) { // Less than 1 day
            $hours = round($diff / 3600);
            if ($hours <= 1) {
                $hours = 1;
            }
            return $hours . ' hour' . ($hours > 1 ? 's' : '');
        } else { // 1 day or more
            $days = round($diff / 86400);
            if ($days <= 1) {
                $days = 1;
            }
            return $days . ' day' . ($days > 1 ? 's' : '');
        }
    }

    /**
     * Scrape miner earnings data from filfox API for multiple miners
     */
    public function scrape_miner_earnings_data()
    {
        try {
            error_log('FIL Platform: Starting miner earnings data scraping for multiple miners...');

            // Define miner addresses and their corresponding database query order
            $miners = [
                [
                    'address' => 'f01083914',
                    'order' => 'ASC',  // First miner (ORDER BY id ASC LIMIT 1)
                    'description' => 'First miner'
                ],
                [
                    'address' => 'f02208475',
                    'order' => 'DESC', // Last miner (ORDER BY id DESC LIMIT 1)
                    'description' => 'Last miner'
                ]
            ];

            $results = [];

            foreach ($miners as $miner) {

                $miner_data = $this->scrape_single_miner_data($miner['address']);

                if ($miner_data['success']) {
                    $results[] = [
                        'address' => $miner['address'],
                        'order' => $miner['order'],
                        'description' => $miner['description'],
                        'data' => $miner_data['data']
                    ];
                } else {
                    error_log('FIL Platform: Failed to scrape data for ' . $miner['description'] . ': ' . $miner_data['error']);
                    // Continue with other miners even if one fails
                }
            }

            if (empty($results)) {
                throw new Exception('Failed to scrape data for all miners');
            }

            return [
                'success' => true,
                'data' => $results
            ];

        } catch (Exception $e) {
            error_log('FIL Platform: Error scraping miner earnings data: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Scrape data for a single miner address
     */
    private function scrape_single_miner_data($miner_address)
    {
        try {
            // API URLs
            $address_api_url = 'https://filfox.info/api/v1/address/' . $miner_address;
            $mining_stats_api_url = 'https://filfox.info/api/v1/address/' . $miner_address . '/mining-stats?duration=24h';

            // Fetch address data
            $address_response = wp_remote_get($address_api_url, [
                'timeout' => 30,
                'headers' => [
                    'User-Agent' => 'FIL Platform WordPress Plugin'
                ]
            ]);

            if (is_wp_error($address_response)) {
                throw new Exception('Failed to fetch address data for ' . $miner_address . ': ' . $address_response->get_error_message());
            }

            $address_body = wp_remote_retrieve_body($address_response);
            $address_data = json_decode($address_body, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Invalid JSON in address response for ' . $miner_address . ': ' . json_last_error_msg());
            }

            // Extract cumulative reward
            $cumulative_reward = isset($address_data['miner']['totalRewards']) ? floatval($address_data['miner']['totalRewards']) / 1e18 : null;

            // Additional fields from address API
            $blockchain_height = $address_data['lastSeenHeight'] ?? null;
            $power = isset($address_data['miner']['qualityAdjPower']) ? floatval($address_data['miner']['qualityAdjPower']) / pow(2, 50) : null;
            $available_balance = isset($address_data['miner']['availableBalance']) ? floatval($address_data['miner']['availableBalance']) / 1e18 : null;
            $pledge_locked = isset($address_data['miner']['initialPledgeRequirement']) ? floatval($address_data['miner']['initialPledgeRequirement']) / 1e18 : null;
            $balance = isset($address_data['balance']) ? floatval($address_data['balance']) / 1e18 : null;

            // Fetch mining stats
            $mining_response = wp_remote_get($mining_stats_api_url, [
                'timeout' => 30,
                'headers' => [
                    'User-Agent' => 'FIL Platform WordPress Plugin'
                ]
            ]);

            if (is_wp_error($mining_response)) {
                throw new Exception('Failed to fetch mining stats for ' . $miner_address . ': ' . $mining_response->get_error_message());
            }

            $mining_body = wp_remote_retrieve_body($mining_response);
            $mining_data = json_decode($mining_body, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Invalid JSON in mining stats response for ' . $miner_address . ': ' . json_last_error_msg());
            }

            $daily_reward = isset($mining_data['totalRewards']) ? floatval($mining_data['totalRewards']) / 1e18 : null;
            $blocks_won = $mining_data['blocksMined'] ?? null;

            return [
                'success' => true,
                'data' => [
                    'cumulative_reward' => $cumulative_reward,
                    'daily_reward' => $daily_reward,
                    'blocks_won' => $blocks_won,
                    'blockchain_height' => $blockchain_height,
                    'power' => $power,
                    'available_balance' => $available_balance,
                    'pledge_locked' => $pledge_locked,
                    'balance' => $balance
                ]
            ];

        } catch (Exception $e) {
            error_log('FIL Platform: Error scraping data for ' . $miner_address . ': ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }


    /**
     * Recursively search for a field in nested array/object structure
     */
    private function find_field_in_response($data, $field_names)
    {
        if (!is_array($data)) {
            return null;
        }

        // First check direct keys
        foreach ($field_names as $field_name) {
            if (isset($data[$field_name])) {
                return $data[$field_name];
            }
        }

        // Then check nested structures
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $result = $this->find_field_in_response($value, $field_names);
                if ($result !== null) {
                    return $result;
                }
            }
        }

        return null;
    }

    /**
     * Store miner earnings data in Supabase for multiple miners
     */
    public function store_miner_earnings($miners_data)
    {
        $supabase_url = get_option('fil_platform_supabase_url');
        $supabase_service_key = get_option('fil_platform_supabase_service_key');

        if (!$supabase_url || !$supabase_service_key) {
            throw new Exception('Supabase configuration not found');
        }

        $stored_count = 0;
        $errors = [];

        foreach ($miners_data as $miner_info) {
            try {
                // Get miner_id based on the order specified
                $order_direction = $miner_info['order']; // 'ASC' or 'DESC'
                $miners_query_url = $supabase_url . '/rest/v1/miners?select=id&order=id.' . strtolower($order_direction) . '&limit=1';

                $miners_response = wp_remote_get($miners_query_url, [
                    'headers' => [
                        'apikey' => $supabase_service_key,
                        'Authorization' => 'Bearer ' . $supabase_service_key,
                        'Content-Type' => 'application/json'
                    ],
                    'timeout' => 30
                ]);

                if (is_wp_error($miners_response)) {
                    throw new Exception('Failed to fetch miner_id for ' . $miner_info['description'] . ': ' . $miners_response->get_error_message());
                }

                // Decode the JSON response
                $miners_response_body = wp_remote_retrieve_body($miners_response);
                $miners_db_data = json_decode($miners_response_body, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new Exception('Invalid JSON response when fetching miner_id for ' . $miner_info['description'] . ': ' . json_last_error_msg());
                }

                if (empty($miners_db_data)) {
                    throw new Exception('No miners found in database for ' . $miner_info['description']);
                }

                $miner_id = $miners_db_data[0]['id'];

                // Prepare payload for miner_daily_earnings
                $payload = [
                    'miner_id' => $miner_id,
                    'earn_date' => date('Y-m-d'), // Current date
                    'cumulative_reward' => floatval($miner_info['data']['cumulative_reward']),
                    'daily_reward' => floatval($miner_info['data']['daily_reward']),
                    'blocks_won' => intval($miner_info['data']['blocks_won'])
                ];

                $payload2 = [
                    'miner_id' => $miner_id,
                    'snapshot_date' => date('Y-m-d'), // Current date
                    'blockchain_height' => floatval($miner_info['data']['blockchain_height']),
                    'power' => floatval($miner_info['data']['power']),
                    'available_balance' => floatval($miner_info['data']['available_balance']),
                    'pledge_locked' => intval($miner_info['data']['pledge_locked']),
                    'balance' => floatval($miner_info['data']['balance'])
                ];

                $response = wp_remote_post($supabase_url . '/rest/v1/miner_daily_earnings', [
                    'headers' => [
                        'apikey' => $supabase_service_key,
                        'Authorization' => 'Bearer ' . $supabase_service_key,
                        'Content-Type' => 'application/json',
                        'Prefer' => 'resolution=merge-duplicates'
                    ],
                    'body' => json_encode($payload),
                    'timeout' => 30
                ]);

                $response = wp_remote_post($supabase_url . '/rest/v1/miner_daily_snapshots', [
                    'headers' => [
                        'apikey' => $supabase_service_key,
                        'Authorization' => 'Bearer ' . $supabase_service_key,
                        'Content-Type' => 'application/json',
                        'Prefer' => 'resolution=merge-duplicates'
                    ],
                    'body' => json_encode($payload2),
                    'timeout' => 30
                ]);

                if (is_wp_error($response)) {
                    throw new Exception('Failed to store data for ' . $miner_info['description'] . ': ' . $response->get_error_message());
                }

                $response_code = wp_remote_retrieve_response_code($response);
                $response_body = wp_remote_retrieve_body($response);

                if ($response_code >= 400) {
                    throw new Exception('Supabase API error for ' . $miner_info['description'] . ': ' . $response_code . ' - ' . $response_body);
                }

                error_log('FIL Platform: Successfully stored data for ' . $miner_info['description']);
                $stored_count++;

            } catch (Exception $e) {
                $error_msg = 'Error storing data for ' . $miner_info['description'] . ': ' . $e->getMessage();
                error_log('FIL Platform: ' . $error_msg);
                $errors[] = $error_msg;
                // Continue with other miners even if one fails
            }
        }

        if ($stored_count === 0) {
            throw new Exception('Failed to store data for all miners. Errors: ' . implode('; ', $errors));
        }

        if (!empty($errors)) {
            error_log('FIL Platform: Some miners failed to store: ' . implode('; ', $errors));
        }

        error_log('FIL Platform: Successfully stored data for ' . $stored_count . ' out of ' . count($miners_data) . ' miners');
    }

    /**
     * AJAX handler for manual miner earnings scrape
     */
    public function ajax_manual_miner_scrape()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        check_ajax_referer('fil_platform_scraper_nonce', 'nonce');

        try {
            $result = $this->scrape_miner_earnings_data();

            if ($result['success']) {
                $this->store_miner_earnings($result['data']);

                // Format response data for better readability
                $response_data = [];
                foreach ($result['data'] as $miner_info) {
                    $response_data[] = [
                        'address' => $miner_info['address'],
                        'description' => $miner_info['description'],
                        'cumulative_reward' => $miner_info['data']['cumulative_reward'],
                        'daily_reward' => $miner_info['data']['daily_reward'],
                        'blocks_won' => $miner_info['data']['blocks_won']
                    ];
                }

                wp_send_json_success([
                    'message' => 'Miner earnings data scraped and stored successfully for ' . count($result['data']) . ' miners',
                    'data' => $response_data
                ]);
            } else {
                wp_send_json_error([
                    'message' => 'Failed to scrape miner earnings data: ' . $result['error']
                ]);
            }
        } catch (Exception $e) {
            wp_send_json_error([
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }
}

// Initialize the scraper
new FIL_Platform_Filfox_Scraper();
