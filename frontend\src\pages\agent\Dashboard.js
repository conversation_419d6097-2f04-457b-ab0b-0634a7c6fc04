import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { getSupabase } from '../../supabaseClient';

const AgentDashboard = () => {
    const { t } = useTranslation();
    const [agentProfile, setAgentProfile] = useState(null);
    const [loading, setLoading] = useState(true);
    const [dashboardStats, setDashboardStats] = useState({
        totalTechCommission: 0,
        totalOpsCommission: 0,
        memberCount: 0,
        totalStorage: 0,
        soldStorage: 0,
        remainingStorage: 0
    });

    const fetchDashboardStats = async (supabase, agentUserId, makerId) => {
        try {

            // 1. 获取会员总数 (customers under this agent)
            const { data: memberData, error: memberError } = await supabase
                .from('customer_profiles')
                .select('user_id', { count: 'exact' })
                .eq('agent_id', agentUserId);

            // 2. 获取该maker的所有产品信息（用于计算总存储）
            const { data: productData, error: productError } = await supabase
                .from('products')
                .select('total_shares, sold_shares')
                .eq('maker_id', makerId);

            // 3. 获取当前agent的佣金余额 (直接从user_assets获取)
            const { data: agentAssets, error: agentAssetsError } = await supabase
                .from('user_assets')
                .select('balance_total')
                .eq('user_id', agentUserId)
                .eq('currency_code', 'FIL')
                .single();

            // 4. 获取technician的佣金余额 (从technician_profiles获取user_id，然后查询user_assets)
            const { data: technicianProfile, error: techError } = await supabase
                .from('technician_profiles')
                .select('user_id')
                .eq('maker_id', makerId)
                .single();

            let technicianAssets = null;
            if (technicianProfile && !techError) {
                const { data: techAssets, error: techAssetsError } = await supabase
                    .from('user_assets')
                    .select('balance_total')
                    .eq('user_id', technicianProfile.user_id)
                    .eq('currency_code', 'FIL')
                    .single();

                if (!techAssetsError) {
                    technicianAssets = techAssets;
                }
            }

            let stats = {
                totalTechCommission: technicianAssets ? (technicianAssets.balance_total || 0) : 0,
                totalOpsCommission: agentAssets ? (agentAssets.balance_total || 0) : 0,
                memberCount: memberData ? memberData.length : 0,
                totalStorage: 0,
                soldStorage: 0,
                remainingStorage: 0
            };

            if (agentAssetsError) {
                console.error('Error fetching agent assets:', agentAssetsError);
            }

            if (techError) {
                console.error('Error fetching technician profile:', techError);
            }

            // 计算存储统计
            if (productData && !productError) {
                productData.forEach(product => {
                    stats.totalStorage += product.total_shares || 0;
                    stats.soldStorage += product.sold_shares || 0;
                });
                stats.remainingStorage = stats.totalStorage - stats.soldStorage;
            }

            setDashboardStats(stats);

        } catch (error) {
            console.error('Error fetching dashboard stats:', error);
        }
    };

    useEffect(() => {
        const fetchAgentData = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                console.log('No user found');
                setLoading(false);
                return; // User not logged in
            }

            // Fetch agent profile
            const { data: profileData, error: profileError } = await supabase
                .from('agent_profiles')
                .select('*')
                .eq('user_id', user.id)
                .single();

            if (profileError) {
                console.error('Error fetching agent profile:', profileError);
                console.error('User ID:', user.id);
                console.error('Error details:', profileError);
                setLoading(false);
                return;
            }

            setAgentProfile(profileData);

            // Fetch dashboard statistics
            await fetchDashboardStats(supabase, user.id, profileData.maker_id);
            setLoading(false);
        };

        fetchAgentData();
    }, []);

    if (loading) {
        return <div>{t('loading_agent_dashboard')}</div>;
    }

    if (!agentProfile) {
        return <div className="alert alert-warning">{t('not_agent')}</div>;
    }

    return (
        <Container fluid>
            <Row className="mb-3">
                <Col>
                    <h2>{t('agent_dashboard')}</h2>
                </Col>
            </Row>

            {/* 第一行：基本信息 */}
            <Row>
                <Col md={6}>
                    <Card className="mb-3">
                        <Card.Body>
                            <Card.Title>{t('brand_name')}</Card.Title>
                            <h3>{agentProfile.brand_name || 'N/A'}</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6}>
                    <Card className="mb-3">
                        <Card.Body>
                            <Card.Title>{t('commission_rate')}</Card.Title>
                            <h3>{agentProfile.commission_pct ? `${agentProfile.commission_pct * 100}%` : 'N/A'}</h3>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* 第二行：佣金统计 */}
            <Row>
                <Col md={6}>
                    <Card className="mb-3">
                        <Card.Body>
                            <Card.Title>{t('total_tech_commission')}</Card.Title>
                            <h3>{dashboardStats.totalTechCommission.toFixed(6)} FIL</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6}>
                    <Card className="mb-3">
                        <Card.Body>
                            <Card.Title>{t('total_ops_commission')}</Card.Title>
                            <h3>{dashboardStats.totalOpsCommission.toFixed(6)} FIL</h3>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* 第三行：会员和存储统计 */}
            <Row>
                <Col md={3}>
                    <Card className="mb-3">
                        <Card.Body>
                            <Card.Title>{t('member_count')}</Card.Title>
                            <h3>{dashboardStats.memberCount}</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="mb-3">
                        <Card.Body>
                            <Card.Title>{t('total_storage')}</Card.Title>
                            <h3>{dashboardStats.totalStorage.toFixed(2)} TiB</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="mb-3">
                        <Card.Body>
                            <Card.Title>{t('sold_storage')}</Card.Title>
                            <h3>{dashboardStats.soldStorage.toFixed(2)} TiB</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={3}>
                    <Card className="text-dark mb-3">
                        <Card.Body>
                            <Card.Title>{t('remaining_storage')}</Card.Title>
                            <h3>{dashboardStats.remainingStorage.toFixed(2)} TiB</h3>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col md={6} className="text-center">
                    <Card>
                        <Card.Body>
                            <h4>{t('member_management')}</h4>
                            <p>{t('my_subordinate_members')}</p>
                            <Button as={Link} to="/agent/member-list">{t('enter_member_list')}</Button>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6} className="text-center">
                    <Card>
                        <Card.Body>
                            <h4>{t('product_management')}</h4>
                            <p>{t('products_on_sale')}</p>
                            <Button as={Link} to="/agent/products">{t('browse_agent_products')}</Button>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default AgentDashboard;