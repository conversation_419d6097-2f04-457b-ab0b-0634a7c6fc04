import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, Button, Modal, Form, Alert } from 'react-bootstrap';
import { getSupabase, getCurrentMakerId } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import { FaTimes, FaPlus } from "react-icons/fa";

const MakerMiners = () => {
    const { t } = useTranslation();
    const [miners, setMiners] = useState([]);
    const [loading, setLoading] = useState(true);

    // Edit modal states
    const [showEditModal, setShowEditModal] = useState(false);
    const [editingMiner, setEditingMiner] = useState(null);
    const [editFormData, setEditFormData] = useState({
        category: '',
        filecoin_miner_id: '',
        sector_size: '',
        effective_until: ''
    });

    // Add modal states
    const [showAddModal, setShowAddModal] = useState(false);
    const [addFormData, setAddFormData] = useState({
        category: '',
        facility_id: '',
        filecoin_miner_id: '',
        sector_size: '',
        effective_until: ''
    });
    const [addError, setAddError] = useState('');
    const [addSuccess, setAddSuccess] = useState('');
    const [addLoading, setAddLoading] = useState(false);
    const [facilities, setFacilities] = useState([]);

    // Delete modal states
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [deletingMiner, setDeletingMiner] = useState(null);
    const [deletePassword, setDeletePassword] = useState('');
    const [deleteLoading, setDeleteLoading] = useState(false);

    // Alert states
    const [alert, setAlert] = useState({ show: false, type: '', message: '' });

    useEffect(() => {
        const fetchMiners = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch miners associated with products from this maker
            const { data, error } = await supabase
                .from('miners')
                .select(`
                    id,
                    category,
                    filecoin_miner_id,
                    sector_size,
                    effective_until,
                    created_at,
                    updated_at,
                    facilities (
                        name
                    )
                `)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching miners:', error);
            } else {
                setMiners(data);
            }
            setLoading(false);
        };

        fetchMiners();
        fetchFacilities();
    }, []);

    // Fetch facilities for dropdown
    const fetchFacilities = async () => {
        const supabase = getSupabase();
        if (!supabase) return;

        try {
            const { data, error } = await supabase
                .from('facilities')
                .select('id, name')
                .order('name', { ascending: true });

            if (error) {
                console.error('Error fetching facilities:', error);
            } else {
                setFacilities(data || []);
            }
        } catch (error) {
            console.error('Error in fetchFacilities:', error);
        }
    };

    // Handle edit button click
    const handleEditClick = (miner) => {
        setEditingMiner(miner);
        setEditFormData({
            category: miner.category || '',
            filecoin_miner_id: miner.filecoin_miner_id || '',
            sector_size: miner.sector_size || '',
            effective_until: miner.effective_until || ''
        });
        setShowEditModal(true);
    };

    // Handle delete button click
    const handleDeleteClick = (miner) => {
        setDeletingMiner(miner);
        setDeletePassword('');
        setShowDeleteModal(true);
    };

    // Handle add button click
    const handleAddClick = () => {
        setAddFormData({
            category: '',
            facility_id: '',
            filecoin_miner_id: '',
            sector_size: '',
            effective_until: ''
        });
        setAddError('');
        setAddSuccess('');
        setShowAddModal(true);
    };

    // Handle add form submission
    const handleAddSubmit = async (e) => {
        e.preventDefault();
        const supabase = getSupabase();
        if (!supabase) return;

        // Validation
        if (!addFormData.category.trim()) {
            setAddError(t('category_required'));
            return;
        }
        if (!addFormData.facility_id) {
            setAddError(t('facility_required'));
            return;
        }
        if (!addFormData.filecoin_miner_id.trim()) {
            setAddError(t('miner_id_required'));
            return;
        }
        if (!addFormData.sector_size || addFormData.sector_size <= 0) {
            setAddError(t('sector_size_required'));
            return;
        }
        if (!addFormData.effective_until) {
            setAddError(t('effective_until_required'));
            return;
        }

        setAddLoading(true);
        setAddError('');
        setAddSuccess('');

        try {
            const { data, error } = await supabase
                .from('miners')
                .insert({
                    category: addFormData.category.trim(),
                    facility_id: addFormData.facility_id,
                    filecoin_miner_id: addFormData.filecoin_miner_id.trim(),
                    sector_size: parseInt(addFormData.sector_size),
                    effective_until: addFormData.effective_until
                })
                .select(`
                    id,
                    category,
                    filecoin_miner_id,
                    sector_size,
                    effective_until,
                    created_at,
                    updated_at,
                    facilities (
                        name
                    )
                `);

            if (error) throw error;

            // Update local state
            setMiners([data[0], ...miners]);
            setAddSuccess(t('miner_added_successfully'));

            // Close modal after 2 seconds
            setTimeout(() => {
                setShowAddModal(false);
                setAddFormData({
                    category: '',
                    facility_id: '',
                    filecoin_miner_id: '',
                    sector_size: '',
                    effective_until: ''
                });
                setAddError('');
                setAddSuccess('');
            }, 2000);

        } catch (error) {
            console.error('Error adding miner:', error);
            setAddError(error.message || t('miner_add_error'));
        } finally {
            setAddLoading(false);
        }
    };

    // Close add modal
    const closeAddModal = () => {
        setShowAddModal(false);
        setAddFormData({
            category: '',
            facility_id: '',
            filecoin_miner_id: '',
            sector_size: '',
            effective_until: ''
        });
        setAddError('');
        setAddSuccess('');
    };

    // Handle edit form submission
    const handleEditSubmit = async (e) => {
        e.preventDefault();
        const supabase = getSupabase();
        if (!supabase || !editingMiner) return;

        try {
            const { error } = await supabase
                .from('miners')
                .update({
                    category: editFormData.category,
                    filecoin_miner_id: editFormData.filecoin_miner_id,
                    sector_size: editFormData.sector_size,
                    effective_until: editFormData.effective_until,
                    updated_at: new Date().toISOString()
                })
                .eq('id', editingMiner.id);

            if (error) throw error;

            // Update local state
            setMiners(miners.map(miner =>
                miner.id === editingMiner.id
                    ? { ...miner, ...editFormData, updated_at: new Date().toISOString() }
                    : miner
            ));

            setAlert({ show: true, type: 'success', message: t('item_updated_successfully') });
            setShowEditModal(false);
            setEditingMiner(null);
        } catch (error) {
            console.error('Error updating miner:', error);
            setAlert({ show: true, type: 'danger', message: t('failed_to_update_item') + ': ' + error.message });
        }
    };

    // Handle delete confirmation
    const handleDeleteConfirm = async () => {
        const supabase = getSupabase();
        if (!supabase || !deletingMiner) return;

        // Validate password input
        if (!deletePassword.trim()) {
            setAlert({ show: true, type: 'danger', message: t('password_required') });
            return;
        }

        setDeleteLoading(true);

        try {
            // Get current user
            const { data: { user }, error: userError } = await supabase.auth.getUser();
            if (userError || !user) {
                throw new Error(t('user_not_authenticated'));
            }

            // Verify password by attempting to sign in
            const { error: signInError } = await supabase.auth.signInWithPassword({
                email: user.email,
                password: deletePassword,
            });

            if (signInError) {
                throw new Error(t('password_incorrect'));
            }

            // Proceed with deletion
            const { error } = await supabase
                .from('miners')
                .delete()
                .eq('id', deletingMiner.id);

            if (error) throw error;

            // Update local state
            setMiners(miners.filter(miner => miner.id !== deletingMiner.id));

            setAlert({ show: true, type: 'success', message: t('item_deleted_successfully') });
            setShowDeleteModal(false);
            setDeletingMiner(null);
            setDeletePassword('');
        } catch (error) {
            console.error('Error deleting miner:', error);
            setAlert({ show: true, type: 'danger', message: error.message || t('failed_to_delete_item') });
        } finally {
            setDeleteLoading(false);
        }
    };

    // Handle form input changes
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setEditFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    // Close alert
    const closeAlert = () => {
        setAlert({ show: false, type: '', message: '' });
    };

    if (loading) {
        return <div>{t('loading_miners')}</div>;
    }

    return (
        <Container>
            {alert.show && (
                <Alert variant={alert.type} dismissible onClose={closeAlert} className="mb-4">
                    {alert.message}
                </Alert>
            )}
            <h2 className="mb-4">{t('all_miners')}</h2>

            {/* Top Operation Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={2}>
                                    <Button
                                        variant="primary"
                                        onClick={handleAddClick}
                                        className="mb-2"
                                    >
                                        <FaPlus className="me-1" />
                                        {t('add_miner')}
                                    </Button>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>{t('category')}</th>
                                        <th>{t('facility')}</th>
                                        <th>{t('miner_id')}</th>
                                        <th>{t('sector_size')}</th>
                                        <th>{t('effective_until')}</th>
                                        <th>{t('created_at')}</th>
                                        <th>{t('updated_at')}</th>
                                        <th>{t('actions')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {miners.length === 0 ? (
                                        <tr>
                                            <td colSpan="9" className="text-center">{t('no_miners_available')}</td>
                                        </tr>
                                    ) : (
                                        miners.map(miner => (
                                            <tr key={miner.id}>
                                                <td>{miner.id.substring(0, 8)}...</td>
                                                <td>{miner.category}</td>
                                                <td>{miner.facilities?.name || '-'}</td>
                                                <td>{miner.filecoin_miner_id}</td>
                                                <td>{miner.sector_size}</td>
                                                <td>{miner.effective_until}</td>
                                                <td>{new Date(miner.created_at).toLocaleString()}</td>
                                                <td>
                                                    {miner.updated_at && !isNaN(new Date(miner.updated_at))
                                                        ? new Date(miner.updated_at).toLocaleString()
                                                        : '–'}
                                                </td>
                                                <td>
                                                    <Button
                                                        variant="info"
                                                        size="sm"
                                                        className="me-2"
                                                        onClick={() => handleEditClick(miner)}
                                                    >
                                                        {t('edit')}
                                                    </Button>
                                                    <Button
                                                        variant="danger"
                                                        size="sm"
                                                        onClick={() => handleDeleteClick(miner)}
                                                    >
                                                        {t('delete')}
                                                    </Button>
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Edit Modal */}
            <Modal show={showEditModal} onHide={() => setShowEditModal(false)} size="lg">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('edit_miner')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={() => setShowEditModal(false)}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    <Form onSubmit={handleEditSubmit}>
                        <Form.Group className="mb-3">
                            <Form.Label>{t('category')}</Form.Label>
                            <Form.Control
                                type="text"
                                name="category"
                                value={editFormData.category}
                                onChange={handleInputChange}
                                required
                            />
                        </Form.Group>
                        <Form.Group className="mb-3">
                            <Form.Label>{t('miner_id')}</Form.Label>
                            <Form.Control
                                type="text"
                                name="filecoin_miner_id"
                                value={editFormData.filecoin_miner_id}
                                onChange={handleInputChange}
                                required
                            />
                        </Form.Group>
                        <Form.Group className="mb-3">
                            <Form.Label>{t('sector_size')}</Form.Label>
                            <Form.Control
                                type="text"
                                name="sector_size"
                                value={editFormData.sector_size}
                                onChange={handleInputChange}
                                required
                            />
                        </Form.Group>
                        <Form.Group className="mb-3">
                            <Form.Label>{t('effective_until')}</Form.Label>
                            <Form.Control
                                type="date"
                                name="effective_until"
                                value={editFormData.effective_until}
                                onChange={handleInputChange}
                                required
                            />
                        </Form.Group>
                        <div className="d-flex justify-content-end">
                            <Button variant="secondary" className="me-2" onClick={() => setShowEditModal(false)}>
                                {t('cancel')}
                            </Button>
                            <Button variant="primary" type="submit">
                                {t('save_changes')}
                            </Button>
                        </div>
                    </Form>
                </Modal.Body>
            </Modal>

            {/* Add Miner Modal */}
            <Modal show={showAddModal} onHide={closeAddModal} size="lg">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('add_miner')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeAddModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {addError && (
                        <Alert variant="danger" className="mb-3">
                            {addError}
                        </Alert>
                    )}
                    {addSuccess && (
                        <Alert variant="success" className="mb-3">
                            {addSuccess}
                        </Alert>
                    )}

                    <Form onSubmit={handleAddSubmit}>
                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('category')}</strong></Form.Label>
                                    <Form.Control
                                        type="text"
                                        value={addFormData.category}
                                        onChange={(e) => setAddFormData({ ...addFormData, category: e.target.value })}
                                        placeholder={t('enter_category')}
                                        disabled={addLoading}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('facility')}</strong></Form.Label>
                                    <Form.Select
                                        value={addFormData.facility_id}
                                        onChange={(e) => setAddFormData({ ...addFormData, facility_id: e.target.value })}
                                        disabled={addLoading}
                                        required
                                    >
                                        <option value="">{t('please_select_facility')}</option>
                                        {facilities.map(facility => (
                                            <option key={facility.id} value={facility.id}>
                                                {facility.name}
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('miner_id')}</strong></Form.Label>
                                    <Form.Control
                                        type="text"
                                        value={addFormData.filecoin_miner_id}
                                        onChange={(e) => setAddFormData({ ...addFormData, filecoin_miner_id: e.target.value })}
                                        placeholder={t('enter_miner_id')}
                                        disabled={addLoading}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('sector_size')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={addFormData.sector_size}
                                        onChange={(e) => setAddFormData({ ...addFormData, sector_size: e.target.value })}
                                        placeholder={t('enter_sector_size')}
                                        disabled={addLoading}
                                        min="1"
                                        required
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Form.Group className="mb-3">
                            <Form.Label><strong>{t('effective_until')}</strong></Form.Label>
                            <Form.Control
                                type="date"
                                value={addFormData.effective_until}
                                onChange={(e) => setAddFormData({ ...addFormData, effective_until: e.target.value })}
                                disabled={addLoading}
                                required
                            />
                        </Form.Group>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeAddModal} disabled={addLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleAddSubmit}
                        disabled={addLoading || !addFormData.category.trim() || !addFormData.facility_id || !addFormData.filecoin_miner_id.trim() || !addFormData.sector_size || !addFormData.effective_until}
                    >
                        {addLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('creating')}
                            </>
                        ) : (
                            <>
                                <FaPlus className="me-1" />
                                {t('create_miner')}
                            </>
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Delete Confirmation Modal */}
            <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('confirm_delete')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={() => setShowDeleteModal(false)}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    <p>{t('delete_confirmation')}</p>
                    {deletingMiner && (
                        <p><strong>{t('miner_id')}: {deletingMiner.filecoin_miner_id}</strong></p>
                    )}
                    <Form.Group className="mt-3">
                        <Form.Label>{t('enter_login_password')}</Form.Label>
                        <Form.Control
                            type="password"
                            value={deletePassword}
                            onChange={(e) => setDeletePassword(e.target.value)}
                            placeholder={t('enter_login_password')}
                            required
                        />
                        <Form.Text className="text-muted">
                            {t('password_confirmation_required')}
                        </Form.Text>
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="danger"
                        onClick={handleDeleteConfirm}
                        disabled={deleteLoading}
                    >
                        {deleteLoading ? t('deleting') : t('confirm')}
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default MakerMiners;
