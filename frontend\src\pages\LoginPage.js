import React, { useState } from 'react';
import { <PERSON>, But<PERSON>, Card, Alert, Modal } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { getSupabase } from '../supabaseClient';
import { useNavigate } from 'react-router-dom';
import { FaEye, FaEyeSlash } from 'react-icons/fa';

const LoginPage = () => {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();

  // 忘记密码相关状态
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [resetEmailSent, setResetEmailSent] = useState(false);
  const [resetError, setResetError] = useState('');
  const [resetLoading, setResetLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const supabase = getSupabase();

      /* ① 登录 */
      const { error: signError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      if (signError) throw signError;

      /* ② 取当前用户 & role */
      const {
        data: { user },
        error: userErr,
      } = await supabase.auth.getUser();
      if (userErr) throw userErr;

      let role = user?.user_metadata?.role;

      // 如果 user_metadata 里没有 role，就去 public.users 表查询
      if (!role) {
        const { data, error: profileErr } = await supabase
          .from('users')
          .select('role')
          .eq('id', user.id)
          .single();
        if (profileErr) throw profileErr;
        role = data.role;
      }

      /* ③ 把 role 存到 localStorage，供前端使用 */
      localStorage.setItem('user_role', role);

      /* ④ 触发自定义事件，通知 App.js 更新角色 */
      window.dispatchEvent(new Event('roleUpdated'));

      /* ⑤ 根据 role 重定向 */
      switch (role) {
        case 'maker':
          navigate('/maker', { replace: true });
          break;
        case 'agent':
          navigate('/agent', { replace: true });
          break;
        case 'technician':
          navigate('/maker', { replace: true }); // technician uses maker pages
          break;
        default:
          navigate('/', { replace: true }); // customer
      }
    } catch (err) {
      console.error('Login Error:', err);
      setError(err.message || t('login_failed'));
    }

    setLoading(false);
  };

  const getCurrentSiteUrl = () => {
    const protocol = window.location.protocol;
    const host = window.location.host;
    return `${protocol}//${host}`;
  };

  // 处理忘记密码
  const handleForgotPassword = async (e) => {
    e.preventDefault();
    setResetError('');
    setResetLoading(true);

    try {
      const supabase = getSupabase();
      if (!supabase) {
        throw new Error('Backend connection failed');
      }

      const siteUrl = getCurrentSiteUrl();

      // 发送密码重置邮件
      const { error } = await supabase.auth.resetPasswordForEmail(resetEmail, {
        redirectTo: `${siteUrl}/fil-platform-app/#/reset-password`,
      });

      if (error) throw error;

      setResetEmailSent(true);
    } catch (err) {
      console.error('Reset Password Error:', err);
      setResetError(err.message || t('reset_email_failed'));
    }

    setResetLoading(false);
  };

  // 关闭忘记密码模态框
  const handleCloseForgotPassword = () => {
    setShowForgotPassword(false);
    setResetEmail('');
    setResetError('');
    setResetEmailSent(false);
  };

  return (
    <div className="login-container">
      <Card className="login-card">
        <Card.Body>
          <Card.Title className="text-center mb-4">{t('login')}</Card.Title>
          {error && <Alert variant="danger">{error}</Alert>}
          <Form onSubmit={handleSubmit}>
            <Form.Group className="mb-3">
              <Form.Label>{t('email_address')}</Form.Label>
              <Form.Control
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>{t('password')}</Form.Label>
              <div style={{ position: 'relative' }}>
                <Form.Control
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  style={{ paddingRight: '40px' }}
                />
                <span
                  onClick={() => setShowPassword(!showPassword)}
                  style={{
                    position: 'absolute',
                    right: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    cursor: 'pointer',
                    color: '#6c757d',
                    fontSize: '16px'
                  }}
                >
                  {showPassword ? <FaEyeSlash /> : <FaEye />}
                </span>
              </div>
            </Form.Group>
            <Button type="submit" className="w-100" disabled={loading}>
              {loading ? t('logging_in') : t('login')}
            </Button>
          </Form>

          {/* 忘记密码链接 */}
          <div className="text-center mt-3">
            <Button
              variant="link"
              className="p-1 text-decoration-none"
              onClick={() => setShowForgotPassword(true)}
            >
              {t('forgot_password')}
            </Button>
          </div>
        </Card.Body>
      </Card>

      {/* 忘记密码模态框 */}
      <Modal show={showForgotPassword} onHide={handleCloseForgotPassword} centered>
        <Modal.Header closeButton>
          <Modal.Title>{t('reset_password')}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {resetEmailSent ? (
            <div className="text-center">
              <Alert variant="success">
                {t('reset_email_sent')}
              </Alert>
              <Button variant="primary" onClick={handleCloseForgotPassword}>
                {t('close')}
              </Button>
            </div>
          ) : (
            <Form onSubmit={handleForgotPassword}>
              <p className="text-muted mb-3">{t('enter_email_for_reset')}</p>
              {resetError && <Alert variant="danger">{resetError}</Alert>}
              <Form.Group className="mb-3">
                <Form.Label>{t('email_address')}</Form.Label>
                <Form.Control
                  type="email"
                  value={resetEmail}
                  onChange={(e) => setResetEmail(e.target.value)}
                  required
                />
              </Form.Group>
              <div className="d-flex gap-2">
                <Button
                  variant="secondary"
                  onClick={handleCloseForgotPassword}
                  disabled={resetLoading}
                >
                  {t('back_to_login')}
                </Button>
                <Button
                  type="submit"
                  variant="primary"
                  disabled={resetLoading}
                  className="flex-grow-1"
                >
                  {resetLoading ? t('sending') : t('send_reset_email')}
                </Button>
              </div>
            </Form>
          )}
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default LoginPage;