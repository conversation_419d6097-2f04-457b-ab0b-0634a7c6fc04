
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Modal, Form, Alert, InputGroup, Pagination } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import { FaTimes, FaPlus, FaDownload } from "react-icons/fa";
import StatusBadge from '../../components/StatusBadge';

const AgentProductListPage = () => {
    const { t } = useTranslation();
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [miners, setMiners] = useState([]);
    const [agentCommissionPct, setAgentCommissionPct] = useState('0.01'); // Store agent's commission percentage

    // Filter states
    const [statusFilter, setStatusFilter] = useState('');
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [filteredProducts, setFilteredProducts] = useState([]);

    // Pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [productsPerPage] = useState(10);
    const [paginatedProducts, setPaginatedProducts] = useState([]);

    // Add Product Modal states
    const [showAddProductModal, setShowAddProductModal] = useState(false);
    const [addProductLoading, setAddProductLoading] = useState(false);
    const [addProductError, setAddProductError] = useState('');
    const [addProductSuccess, setAddProductSuccess] = useState('');

    // Form data
    const [productForm, setProductForm] = useState({
        name: '',
        total_shares: '',
        miner_id: '',
        price: '',
        effective_delay_days: '0',
        min_purchase: '',
        partner_reward_pct: '0',
        ops_commission_pct: '0.25',
        tech_commission_pct: '0.05',
        commission_agent_pct: '0.01'
    });

    useEffect(() => {
        const fetchData = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);

            // Get current user (agent)
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                setLoading(false);
                return;
            }

            // Get agent profile to fetch commission_pct
            const { data: agentProfile, error: agentError } = await supabase
                .from('agent_profiles')
                .select('commission_pct')
                .eq('user_id', user.id)
                .single();

            if (agentError) {
                console.error('Error fetching agent profile:', agentError);
            } else if (agentProfile) {
                const commissionPct = agentProfile.commission_pct.toString();
                setAgentCommissionPct(commissionPct);
                // Update the initial form state with agent's commission percentage
                setProductForm(prev => ({
                    ...prev,
                    commission_agent_pct: commissionPct
                }));
            }

            // Fetch products for this agent
            const { data: productsData, error: productsError } = await supabase
                .from('products')
                .select(`
                    id,
                    name,
                    category,
                    price,
                    total_shares,
                    sold_shares,
                    is_disabled,
                    review_status,
                    created_at,
                    duration_days,
                    maker_profiles ( domain )
                `)
                .eq('agent_id', user.id)
                .order('created_at', { ascending: false });

            if (productsError) {
                console.error('Error fetching products:', productsError);
            } else {
                setProducts(productsData || []);
            }

            // Fetch miners for dropdown
            const { data: minersData, error: minersError } = await supabase
                .from('miners')
                .select('id, filecoin_miner_id, category')
                .order('filecoin_miner_id');

            if (minersError) {
                console.error('Error fetching miners:', minersError);
            } else {
                setMiners(minersData);
            }

            setLoading(false);
        };

        fetchData();
    }, []);

    // Filter products based on search criteria
    useEffect(() => {
        let filtered = products;

        // Filter by review status
        if (statusFilter) {
            filtered = filtered.filter(product => product.review_status === statusFilter);
        }

        // Filter by date range
        if (startDate) {
            filtered = filtered.filter(product =>
                new Date(product.created_at) >= new Date(startDate)
            );
        }
        if (endDate) {
            filtered = filtered.filter(product =>
                new Date(product.created_at) <= new Date(endDate)
            );
        }

        setFilteredProducts(filtered);
        setCurrentPage(1); // Reset to first page when filters change
    }, [products, statusFilter, startDate, endDate]);

    // Paginate filtered products
    useEffect(() => {
        const indexOfLastProduct = currentPage * productsPerPage;
        const indexOfFirstProduct = indexOfLastProduct - productsPerPage;
        const currentProducts = filteredProducts.slice(indexOfFirstProduct, indexOfLastProduct);
        setPaginatedProducts(currentProducts);
    }, [filteredProducts, currentPage, productsPerPage]);

    // Pagination handlers
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const totalPages = Math.ceil(filteredProducts.length / productsPerPage);

    // Export filtered products to CSV
    const exportToCSV = () => {
        if (filteredProducts.length === 0) {
            alert(t('no_data_to_export'));
            return;
        }

        // Define CSV headers
        const headers = [
            t('product_id'),
            t('product_name'),
            t('category'),
            t('price'),
            t('total_shares'),
            t('sold_shares'),
            t('remaining_shares'),
            t('duration_days'),
            t('maker'),
            t('review_status'),
            t('created_at')
        ];

        // Convert data to CSV format
        const csvData = filteredProducts.map(product => [
            product.id,
            product.name,
            product.category,
            product.price || '0',
            product.total_shares || '0',
            product.sold_shares || '0',
            (product.total_shares - product.sold_shares) || '0',
            product.duration_days || '0',
            product.maker_profiles?.domain || 'N/A',
            t(product.review_status) || 'pending',
            new Date(product.created_at).toLocaleString()
        ]);

        // Combine headers and data
        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Add UTF-8 BOM to ensure proper encoding for Japanese characters
        const BOM = '\uFEFF';
        const csvWithBOM = BOM + csvContent;

        // Create and download CSV file with proper UTF-8 encoding
        const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `agent_products_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handleAddProduct = () => {
        setShowAddProductModal(true);
        setProductForm({
            name: '',
            total_shares: '',
            miner_id: '',
            price: '',
            effective_delay_days: '0',
            min_purchase: '',
            partner_reward_pct: '0',
            ops_commission_pct: '0.25',
            tech_commission_pct: '0.05',
            commission_agent_pct: agentCommissionPct, // Use agent's commission percentage
            duration_days: '1',
            category: 'FIL'
        });
        setAddProductError('');
        setAddProductSuccess('');
    };

    const handleFormChange = (field, value) => {
        setProductForm(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const validateForm = () => {
        if (!productForm.name.trim()) {
            setAddProductError(t('product_name_required'));
            return false;
        }
        if (!productForm.total_shares || parseFloat(productForm.total_shares) <= 0) {
            setAddProductError(t('total_shares_required'));
            return false;
        }
        if (!productForm.miner_id) {
            setAddProductError(t('miner_selection_required'));
            return false;
        }
        if (!productForm.price || parseFloat(productForm.price) <= 0) {
            setAddProductError(t('price_required'));
            return false;
        }
        if (!productForm.min_purchase || parseFloat(productForm.min_purchase) <= 0) {
            setAddProductError(t('min_purchase_required'));
            return false;
        }
        if (parseFloat(productForm.min_purchase) > parseFloat(productForm.total_shares)) {
            setAddProductError(t('min_purchase_cannot_exceed_total_shares'));
            return false;
        }
        return true;
    };

    const handleConfirmAddProduct = async () => {
        if (!validateForm()) {
            return;
        }

        setAddProductLoading(true);
        setAddProductError('');
        setAddProductSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Get current user (agent)
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                throw new Error('Agent not authenticated');
            }

            // Get agent profile to get maker_id
            const { data: agentProfile, error: agentError } = await supabase
                .from('agent_profiles')
                .select('maker_id')
                .eq('user_id', user.id)
                .single();

            if (agentError || !agentProfile) {
                throw new Error('Agent profile not found');
            }

            // Get default technician for this maker
            const { data: technicianProfile, error: techError } = await supabase
                .from('technician_profiles')
                .select('user_id')
                .eq('maker_id', agentProfile.maker_id)
                .limit(1)
                .single();

            if (techError) {
                console.error('Error fetching technician profile:', techError);
                throw new Error('No technician found for this maker');
            }

            // Prepare product data
            const productData = {
                maker_id: agentProfile.maker_id,
                agent_id: user.id,
                technician_id: technicianProfile.user_id,
                category: productForm.category || 'FIL',
                name: productForm.name.trim(),
                total_shares: parseFloat(productForm.total_shares),
                miner_id: productForm.miner_id,
                price: parseFloat(productForm.price),
                effective_delay_days: parseInt(productForm.effective_delay_days) || 0,
                min_purchase: parseFloat(productForm.min_purchase),
                sold_shares: 0,
                partner_reward_pct: parseFloat(productForm.partner_reward_pct) || 0,
                ops_commission_pct: parseFloat(productForm.ops_commission_pct) || 0.25,
                tech_commission_pct: parseFloat(productForm.tech_commission_pct) || 0.05,
                commission_agent_pct: parseFloat(agentCommissionPct) || 0.01, // Use agent's commission percentage
                duration_days: parseInt(productForm.duration_days) || 1,
                is_disabled: false,
                self_distribution: true,
                auto_distribution: true,
                review_status: 'pending'
            };

            // Insert product
            const { data, error } = await supabase
                .from('products')
                .insert(productData)
                .select(`
                    id,
                    name,
                    category,
                    price,
                    total_shares,
                    sold_shares,
                    is_disabled,
                    review_status,
                    created_at,
                    duration_days,
                    maker_profiles ( domain )
                `)
                .single();

            if (error) {
                throw error;
            }

            setAddProductSuccess(t('product_created_successfully'));

            // Update products list
            setProducts(prev => [data, ...prev]);

            // Close modal after 2 seconds
            setTimeout(() => {
                setShowAddProductModal(false);
            }, 2000);

        } catch (error) {
            console.error('Error creating product:', error);
            setAddProductError(error.message || t('product_creation_error'));
        } finally {
            setAddProductLoading(false);
        }
    };

    const closeAddProductModal = () => {
        setShowAddProductModal(false);
        setProductForm({
            name: '',
            total_shares: '',
            miner_id: '',
            price: '',
            effective_delay_days: '0',
            min_purchase: '',
            partner_reward_pct: '0',
            ops_commission_pct: '0.25',
            tech_commission_pct: '0.05',
            commission_agent_pct: agentCommissionPct, // Use agent's commission percentage
            duration_days: '1',
            category: 'FIL'
        });
        setAddProductError('');
        setAddProductSuccess('');
    };

    if (loading) {
        return <div>{t('loading_products')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('products_on_sale')}</h2>

            {/* Filter Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('export_data')}</Form.Label>
                                        <div>
                                            <Button
                                                variant="success"
                                                onClick={exportToCSV}
                                                className="mb-2 me-2"
                                                disabled={filteredProducts.length === 0}
                                            >
                                                <FaDownload className="me-1" />
                                                {t('export_all')}
                                            </Button>
                                            <Button
                                                variant="primary"
                                                onClick={handleAddProduct}
                                                className="mb-2"
                                            >
                                                <FaPlus className="me-1" />
                                                {t('add_new_product')}
                                            </Button>
                                        </div>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('review_status')}</Form.Label>
                                        <Form.Select
                                            value={statusFilter}
                                            onChange={(e) => setStatusFilter(e.target.value)}
                                        >
                                            <option value="">{t('all_status')}</option>
                                            <option value="pending">{t('pending_review')}</option>
                                            <option value="approved">{t('approved')}</option>
                                            <option value="rejected">{t('rejected')}</option>
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('start_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('end_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('product_id')}</th>
                                        <th>{t('product_name')}</th>
                                        <th>{t('category')}</th>
                                        <th>{t('price')}</th>
                                        <th>{t('total_shares')}</th>
                                        <th>{t('sold_shares')}</th>
                                        <th>{t('remaining_shares')}</th>
                                        <th>{t('duration_days')}</th>
                                        <th>{t('maker')}</th>
                                        <th>{t('review_status')}</th>
                                        <th>{t('created_at')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedProducts.length === 0 ? (
                                        <tr>
                                            <td colSpan="11" className="text-center">{t('no_products')}</td>
                                        </tr>
                                    ) : (
                                        paginatedProducts.map(product => (
                                            <tr key={product.id}>
                                                <td>{product.id.substring(0, 8)}...</td>
                                                <td>{product.name}</td>
                                                <td>{product.category}</td>
                                                <td>{product.price}</td>
                                                <td>{product.total_shares}</td>
                                                <td>{product.sold_shares}</td>
                                                <td>{product.total_shares - product.sold_shares}</td>
                                                <td>{product.duration_days}</td>
                                                <td>{product.maker_profiles?.domain || 'N/A'}</td>
                                                <td><StatusBadge status={product.review_status} type="review" /></td>
                                                <td>{new Date(product.created_at).toLocaleString()}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="d-flex justify-content-center">
                                    <Pagination>
                                        <Pagination.First
                                            onClick={() => handlePageChange(1)}
                                            disabled={currentPage === 1}
                                        />
                                        <Pagination.Prev
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                        />

                                        {/* Show page numbers */}
                                        {[...Array(totalPages)].map((_, index) => {
                                            const pageNumber = index + 1;
                                            // Show first page, last page, current page, and pages around current page
                                            if (
                                                pageNumber === 1 ||
                                                pageNumber === totalPages ||
                                                (pageNumber >= currentPage - 2 && pageNumber <= currentPage + 2)
                                            ) {
                                                return (
                                                    <Pagination.Item
                                                        key={pageNumber}
                                                        active={pageNumber === currentPage}
                                                        onClick={() => handlePageChange(pageNumber)}
                                                    >
                                                        {pageNumber}
                                                    </Pagination.Item>
                                                );
                                            } else if (
                                                pageNumber === currentPage - 3 ||
                                                pageNumber === currentPage + 3
                                            ) {
                                                return <Pagination.Ellipsis key={pageNumber} />;
                                            }
                                            return null;
                                        })}

                                        <Pagination.Next
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                        />
                                        <Pagination.Last
                                            onClick={() => handlePageChange(totalPages)}
                                            disabled={currentPage === totalPages}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Add Product Modal */}
            <Modal show={showAddProductModal} onHide={closeAddProductModal} size="lg">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('add_new_product')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeAddProductModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {addProductError && (
                        <Alert variant="danger" className="mb-3">
                            {addProductError}
                        </Alert>
                    )}
                    {addProductSuccess && (
                        <Alert variant="success" className="mb-3">
                            {addProductSuccess}
                        </Alert>
                    )}

                    <Form>
                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('product_name')}</strong></Form.Label>
                                    <Form.Control
                                        type="text"
                                        value={productForm.name}
                                        onChange={(e) => handleFormChange('name', e.target.value)}
                                        placeholder={t('enter_product_name')}
                                        disabled={addProductLoading}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('total_shares')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.total_shares}
                                        onChange={(e) => handleFormChange('total_shares', e.target.value)}
                                        placeholder={t('enter_total_shares')}
                                        disabled={addProductLoading}
                                        min="1"
                                        step="1"
                                        required
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('miner_selection')}</strong></Form.Label>
                                    <Form.Select
                                        value={productForm.miner_id}
                                        onChange={(e) => handleFormChange('miner_id', e.target.value)}
                                        disabled={addProductLoading}
                                        required
                                    >
                                        <option value="">{t('select_miner')}</option>
                                        {miners.map(miner => (
                                            <option key={miner.id} value={miner.id}>
                                                {miner.filecoin_miner_id} ({miner.category})
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('price')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.price}
                                        onChange={(e) => handleFormChange('price', e.target.value)}
                                        placeholder={t('enter_price')}
                                        disabled={addProductLoading}
                                        min="0"
                                        step="0.01"
                                        required
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('effective_delay_days')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.effective_delay_days}
                                        onChange={(e) => handleFormChange('effective_delay_days', e.target.value)}
                                        placeholder="0"
                                        disabled={addProductLoading}
                                        min="0"
                                        step="1"
                                    />
                                    <Form.Text className="text-muted">
                                        {t('default_0_days')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('min_purchase')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.min_purchase}
                                        onChange={(e) => handleFormChange('min_purchase', e.target.value)}
                                        placeholder={t('enter_min_purchase')}
                                        disabled={addProductLoading}
                                        min="1"
                                        step="1"
                                        required
                                    />
                                    <Form.Text className="text-muted">
                                        {t('cannot_exceed_total_shares')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('product_category')}</strong></Form.Label>
                                    <Form.Control
                                        type="text"
                                        value={productForm.category}
                                        onChange={(e) => handleFormChange('category', e.target.value)}
                                        placeholder="FIL"
                                        disabled={addProductLoading}
                                        required
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('duration_days')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.duration_days}
                                        onChange={(e) => handleFormChange('duration_days', e.target.value)}
                                        placeholder={t('enter_duration_days')}
                                        disabled={addProductLoading}
                                        min="1"
                                        step="1"
                                        required
                                    />
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('partner_reward_pct')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.partner_reward_pct}
                                        onChange={(e) => handleFormChange('partner_reward_pct', e.target.value)}
                                        placeholder="0"
                                        disabled={addProductLoading}
                                        min="0"
                                        max="1"
                                        step="0.01"
                                    />
                                    <Form.Text className="text-muted">
                                        {t('default_0_percent')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('ops_commission_pct')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.ops_commission_pct}
                                        onChange={(e) => handleFormChange('ops_commission_pct', e.target.value)}
                                        placeholder="0.25"
                                        disabled={addProductLoading}
                                        min="0"
                                        max="1"
                                        step="0.01"
                                    />
                                    <Form.Text className="text-muted">
                                        {t('default_25_percent')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('tech_commission_pct')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.tech_commission_pct}
                                        onChange={(e) => handleFormChange('tech_commission_pct', e.target.value)}
                                        placeholder="0.05"
                                        disabled={addProductLoading}
                                        min="0"
                                        max="1"
                                        step="0.01"
                                    />
                                    <Form.Text className="text-muted">
                                        {t('default_5_percent')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('commission_agent_pct')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={productForm.commission_agent_pct}
                                        placeholder="0.01"
                                        disabled={true} // Always disabled - uses agent's commission percentage
                                        min="0"
                                        max="1"
                                        step="0.01"
                                    />
                                    <Form.Text className="text-muted">
                                        {t('agent_commission_auto_set')}
                                    </Form.Text>
                                </Form.Group>
                            </Col>
                        </Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeAddProductModal} disabled={addProductLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleConfirmAddProduct}
                        disabled={addProductLoading || !productForm.name || !productForm.total_shares || !productForm.miner_id || !productForm.price || !productForm.min_purchase}
                    >
                        {addProductLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('creating')}
                            </>
                        ) : (
                            t('create_product')
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default AgentProductListPage;
