import React, { useState, useEffect } from 'react';
import { Form, Button, Card, Alert } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { FaEye, FaEyeSlash } from 'react-icons/fa';

const RegisterPage = () => {
  const { t } = useTranslation();
  const { inviteCode } = useParams();
  const navigate = useNavigate();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  useEffect(() => {
    // Check if invite code is provided
    if (!inviteCode) {
      setError(t('invalid_invite_code'));
    }
  }, [inviteCode, t]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setLoading(true);

    // Validate form
    if (!email || !password || !confirmPassword) {
      setError(t('all_fields_required'));
      setLoading(false);
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError(t('invalid_email_format'));
      setLoading(false);
      return;
    }

    // Validate password length
    if (password.length < 6) {
      setError(t('password_min_length'));
      setLoading(false);
      return;
    }

    // Validate password match
    if (password !== confirmPassword) {
      setError(t('password_mismatch'));
      setLoading(false);
      return;
    }

    try {
      // Call registration API
      const response = await fetch(`${window.wpData.apiUrl}register-customer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-WP-Nonce': window.wpData.nonce
        },
        body: JSON.stringify({
          email: email,
          password: password,
          invite_code: inviteCode
        })
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        // Handle specific error codes
        if (result.error_code) {
          setError(t(result.error_code));
        } else {
          setError(result.message || t('registration_failed'));
        }
        setLoading(false);
        return;
      }

      // Registration successful
      setSuccess(t('registration_successful'));
      
      // Redirect to login page after 2 seconds
      setTimeout(() => {
        navigate('/login');
      }, 2000);

    } catch (err) {
      console.error('Registration Error:', err);
      setError(t('registration_failed'));
    }

    setLoading(false);
  };

  return (
    <div className="d-flex justify-content-center align-items-center vh-100">
      <Card style={{ width: '400px' }}>
        <Card.Body>
          <Card.Title className="text-center mb-4">{t('register_with_invite')}</Card.Title>
          {error && <Alert variant="danger">{error}</Alert>}
          {success && <Alert variant="success">{success}</Alert>}
          <Form onSubmit={handleSubmit}>
            <Form.Group className="mb-3">
              <Form.Label><span className="text-danger">*</span>{t('email_address')}</Form.Label>
              <Form.Control
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={loading}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label><span className="text-danger">*</span>{t('password')}</Form.Label>
              <div style={{ position: 'relative' }}>
                <Form.Control
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  disabled={loading}
                  style={{ paddingRight: '40px' }}
                />
                <span
                  onClick={() => setShowPassword(!showPassword)}
                  style={{
                    position: 'absolute',
                    right: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    cursor: loading ? 'not-allowed' : 'pointer',
                    color: loading ? '#adb5bd' : '#6c757d',
                    fontSize: '16px',
                    pointerEvents: loading ? 'none' : 'auto'
                  }}
                >
                  {showPassword ? <FaEyeSlash /> : <FaEye />}
                </span>
              </div>
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label><span className="text-danger">*</span>{t('confirm_password')}</Form.Label>
              <div style={{ position: 'relative' }}>
                <Form.Control
                  type={showConfirmPassword ? "text" : "password"}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  disabled={loading}
                  style={{ paddingRight: '40px' }}
                />
                <span
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  style={{
                    position: 'absolute',
                    right: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    cursor: loading ? 'not-allowed' : 'pointer',
                    color: loading ? '#adb5bd' : '#6c757d',
                    fontSize: '16px',
                    pointerEvents: loading ? 'none' : 'auto'
                  }}
                >
                  {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                </span>
              </div>
            </Form.Group>
            <Button type="submit" className="w-100" disabled={loading || !inviteCode}>
              {loading ? t('registering') : t('register')}
            </Button>
          </Form>
          <div className="text-center mt-3">
            <small>
              {t('already_have_account')} <Link to="/login">{t('login_here')}</Link>
            </small>
          </div>
        </Card.Body>
      </Card>
    </div>
  );
};

export default RegisterPage;
