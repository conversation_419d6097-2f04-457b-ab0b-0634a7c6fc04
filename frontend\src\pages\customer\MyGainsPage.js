import React, { useState, useEffect, useMemo } from 'react';
import { Container, Row, Col, Button, Form } from 'react-bootstrap';
import { DataGrid } from '@mui/x-data-grid';
import { Box, Chip, TextField, MenuItem, IconButton } from '@mui/material';
import { Download as DownloadIcon, FilterAlt as FilterIcon } from '@mui/icons-material';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import { zhCN, jaJP, enUS } from '@mui/x-data-grid/locales';

const MyGainsPage = () => {
    const { t, i18n } = useTranslation();
    const [earnings, setEarnings] = useState([]);
    const [loading, setLoading] = useState(true);
    const [orders, setOrders] = useState([]);
    const [selectedMonth, setSelectedMonth] = useState('all');
    const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

    // Get MUI DataGrid locale based on current language
    const getDataGridLocale = () => {
        switch (i18n.language) {
            case 'zh':
                return zhCN.components.MuiDataGrid.defaultProps.localeText;
            case 'ja':
                return jaJP.components.MuiDataGrid.defaultProps.localeText;
            default:
                return enUS.components.MuiDataGrid.defaultProps.localeText;
        }
    };

    // Get available months and years from earnings data
    const availableMonths = useMemo(() => {
        const months = new Set();
        const years = new Set();
        
        earnings.forEach(earning => {
            const date = new Date(earning.created_at);
            months.add(date.getMonth());
            years.add(date.getFullYear());
        });

        return {
            months: Array.from(months).sort((a, b) => a - b),
            years: Array.from(years).sort((a, b) => b - a)
        };
    }, [earnings]);

    // Filter earnings based on selected month and year
    const filteredEarnings = useMemo(() => {
        if (selectedMonth === 'all') return earnings;
        
        return earnings.filter(earning => {
            const date = new Date(earning.created_at);
            const month = date.getMonth();
            const year = date.getFullYear();
            
            return month === parseInt(selectedMonth) && year === selectedYear;
        });
    }, [earnings, selectedMonth, selectedYear]);

    // Filter orders based on selected month and year
    const filteredOrders = useMemo(() => {
        if (selectedMonth === 'all') return orders;
        
        return orders.filter(order => {
            const date = new Date(order.created_at);
            const month = date.getMonth();
            const year = date.getFullYear();
            
            return month === parseInt(selectedMonth) && year === selectedYear;
        });
    }, [orders, selectedMonth, selectedYear]);

    // CSV Export function with proper encoding and formatting
    const exportToCSV = (data, filename, isEarnings = true) => {
        if (data.length === 0) {
            alert(t('no_data_to_export') || 'No data to export');
            return;
        }

        let csvContent = '';
        
        if (isEarnings) {
            // Earnings CSV headers
            const headers = [
                t('gain_id') || 'Gain ID',
                t('order_id') || 'Order ID',
                t('shares') || 'Shares',
                t('gain_amount') || 'Gain Amount',
                t('fee') || 'Fee',
                t('progress') || 'Progress',
                t('time') || 'Time'
            ];
            csvContent = headers.join(',') + '\n';
            
            // Earnings CSV data
            data.forEach(row => {
                const formatDate = (dateString) => {
                    const date = new Date(dateString);
                    return date.getFullYear() + '-' + 
                           String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                           String(date.getDate()).padStart(2, '0') + ' ' +
                           String(date.getHours()).padStart(2, '0') + ':' + 
                           String(date.getMinutes()).padStart(2, '0');
                };

                const rowData = [
                    `"${row.id}"`,
                    `"${row.order_id || 'N/A'}"`,
                    row.share_amount || 0,
                    row.reward_amount || 0,
                    row.fee_amount || 0,
                    `${(row.progress * 100).toFixed(0)}%`,
                    `"${formatDate(row.created_at)}"`
                ];
                csvContent += rowData.join(',') + '\n';
            });
        } else {
            // Orders CSV headers
            const headers = [
                t('order_id') || 'Order ID',
                t('product_name') || 'Product Name',
                t('shares') || 'Shares',
                t('storage_cost') || 'Storage Cost',
                t('pledge_cost') || 'Pledge Cost',
                t('total_rate') || 'Total Rate',
                t('start_date') || 'Start Date',
                t('end_date') || 'End Date',
                t('status') || 'Status',
                t('created_at') || 'Created At'
            ];
            csvContent = headers.join(',') + '\n';
            
            // Orders CSV data
            data.forEach(row => {
                const formatDate = (dateString) => {
                    const date = new Date(dateString);
                    return date.getFullYear() + '-' + 
                           String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                           String(date.getDate()).padStart(2, '0');
                };

                const formatDateTime = (dateString) => {
                    const date = new Date(dateString);
                    return date.getFullYear() + '-' + 
                           String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                           String(date.getDate()).padStart(2, '0') + ' ' +
                           String(date.getHours()).padStart(2, '0') + ':' + 
                           String(date.getMinutes()).padStart(2, '0');
                };

                const rowData = [
                    `"${row.id}"`,
                    `"${row.products?.name || 'N/A'}"`,
                    row.shares || 0,
                    row.storage_cost || 0,
                    row.pledge_cost || 0,
                    row.total_rate || 0,
                    `"${formatDate(row.start_at)}"`,
                    `"${formatDate(row.end_at)}"`,
                    `"${t(row.review_status) || row.review_status}"`,
                    `"${formatDateTime(row.created_at)}"`
                ];
                csvContent += rowData.join(',') + '\n';
            });
        }

        // Add BOM for UTF-8 encoding to handle special characters properly
        const BOM = '\uFEFF';
        const csvContentWithBOM = BOM + csvContent;

        // Create and download CSV file with proper encoding
        const blob = new Blob([csvContentWithBOM], { 
            type: 'text/csv;charset=utf-8;' 
        });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    };

    const handleExportEarnings = () => {
        const currentDate = new Date();
        const dateString = currentDate.toISOString().split('T')[0];
        const monthFilter = selectedMonth === 'all' ? 'all' : `${selectedYear}-${String(parseInt(selectedMonth) + 1).padStart(2, '0')}`;
        const filename = `earnings_${monthFilter}_${dateString}.csv`;
        exportToCSV(filteredEarnings, filename, true);
    };

    const handleExportOrders = () => {
        const currentDate = new Date();
        const dateString = currentDate.toISOString().split('T')[0];
        const monthFilter = selectedMonth === 'all' ? 'all' : `${selectedYear}-${String(parseInt(selectedMonth) + 1).padStart(2, '0')}`;
        const filename = `orders_${monthFilter}_${dateString}.csv`;
        exportToCSV(filteredOrders, filename, false);
    };

    // Month names for display
    const getMonthName = (monthIndex) => {
        const date = new Date();
        date.setMonth(monthIndex);
        return date.toLocaleDateString(i18n.language === 'zh' ? 'zh-CN' : i18n.language === 'ja' ? 'ja-JP' : 'en-US', { month: 'long' });
    };

    useEffect(() => {
        const fetchEarnings = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            const { data, error } = await supabase
                .from('order_distributions') // Assuming this table holds customer earnings
                .select(`
                    id,
                    batch_id,
                    order_id,
                    customer_id,
                    share_amount,
                    reward_amount,
                    fee_amount,
                    progress,
                    created_at
                `)
                .eq('customer_id', user.id)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching earnings:', error);
            } else {
                setEarnings(data);
            }
            setLoading(false);
        };

        const fetchOrders = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            const { data, error } = await supabase
                .from('orders')
                .select(`
                            id,
                            shares,
                            storage_cost,
                            pledge_cost,
                            total_rate,
                            start_at,
                            end_at,
                            review_status,
                            created_at,
                            products ( name )
                        `)
                .eq('customer_id', user.id)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching orders:', error);
            } else {
                setOrders(data);
            }
            setLoading(false);
        };

        fetchOrders();
        fetchEarnings();
    }, []);

    // Define columns for earnings DataGrid
    const earningsColumns = [
        {
            field: 'id',
            headerName: t('gain_id'),
            width: 150,
            renderCell: (params) => params.value.substring(0, 8) + '...'
        },
        {
            field: 'order_id',
            headerName: t('order_id'),
            width: 150,
            renderCell: (params) => params.value ? params.value.substring(0, 8) + '...' : 'N/A'
        },
        {
            field: 'share_amount',
            headerName: t('shares'),
            width: 120,
            type: 'number'
        },
        {
            field: 'reward_amount',
            headerName: t('gain_amount'),
            width: 150,
            type: 'number'
        },
        {
            field: 'fee_amount',
            headerName: t('fee'),
            width: 120,
            type: 'number'
        },
        {
            field: 'progress',
            headerName: t('progress'),
            width: 120,
            renderCell: (params) => (
                <Chip
                    label={`${(params.value * 100).toFixed(0)}%`}
                    color={params.value === 1 ? 'success' : 'info'}
                    size="small"
                />
            )
        },
        {
            field: 'created_at',
            headerName: t('time'),
            width: 180,
            renderCell: (params) => new Date(params.value).toLocaleString()
        }
    ];

    // Define columns for orders DataGrid
    const ordersColumns = [
        {
            field: 'id',
            headerName: t('order_id'),
            width: 150,
            renderCell: (params) => params.value.substring(0, 8) + '...'
        },
        {
            field: 'product_name',
            headerName: t('product_name'),
            width: 200,
            renderCell: (params) => params.row.products?.name || 'N/A'
        },
        {
            field: 'shares',
            headerName: t('shares'),
            width: 120,
            type: 'number'
        },
        {
            field: 'storage_cost',
            headerName: t('storage_cost'),
            width: 150,
            type: 'number'
        },
        {
            field: 'pledge_cost',
            headerName: t('pledge_cost'),
            width: 150,
            type: 'number'
        },
        {
            field: 'total_rate',
            headerName: t('total_rate'),
            width: 120,
            type: 'number'
        },
        {
            field: 'start_at',
            headerName: t('start_date'),
            width: 150,
            renderCell: (params) => new Date(params.value).toLocaleDateString()
        },
        {
            field: 'end_at',
            headerName: t('end_date'),
            width: 150,
            renderCell: (params) => new Date(params.value).toLocaleDateString()
        },
        {
            field: 'review_status',
            headerName: t('status'),
            width: 120,
            renderCell: (params) => (
                <Chip
                    label={t(params.value)}
                    color={params.value === 'approved' ? 'success' : 'warning'}
                    size="small"
                />
            )
        },
        {
            field: 'created_at',
            headerName: t('created_at'),
            width: 180,
            renderCell: (params) => new Date(params.value).toLocaleString()
        }
    ];

    if (loading) {
        return <div>{t('loading_earnings')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('my_gains')}</h2>
            
            {/* Filter Controls */}
            <Row className="mb-3">
                <Col md={3}>
                    <TextField
                        select
                        label={t('select_year') || 'Select Year'}
                        value={selectedYear}
                        onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                        size="small"
                        fullWidth
                        variant="outlined"
                    >
                        {availableMonths.years.map((year) => (
                            <MenuItem key={year} value={year}>
                                {year}
                            </MenuItem>
                        ))}
                    </TextField>
                </Col>
                <Col md={3}>
                    <TextField
                        select
                        label={t('select_month') || 'Select Month'}
                        value={selectedMonth}
                        onChange={(e) => setSelectedMonth(e.target.value)}
                        size="small"
                        fullWidth
                        variant="outlined"
                    >
                        <MenuItem value="all">{t('all_months') || 'All Months'}</MenuItem>
                        {availableMonths.months.map((monthIndex) => (
                            <MenuItem key={monthIndex} value={monthIndex}>
                                {getMonthName(monthIndex)}
                            </MenuItem>
                        ))}
                    </TextField>
                </Col>
                <Col md={6} className="d-flex align-items-end">
                    <Button
                        variant="outline-primary"
                        onClick={handleExportEarnings}
                        className="me-2"
                        disabled={filteredEarnings.length === 0}
                    >
                        <DownloadIcon style={{ marginRight: 8 }} />
                        {t('export_earnings_csv') || 'Export Earnings CSV'}
                    </Button>
                    <Button
                        variant="outline-secondary"
                        onClick={handleExportOrders}
                        disabled={filteredOrders.length === 0}
                    >
                        <DownloadIcon style={{ marginRight: 8 }} />
                        {t('export_orders_csv') || 'Export Orders CSV'}
                    </Button>
                </Col>
            </Row>

            {/* Filter Summary */}
            <Row className="mb-3">
                <Col>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <FilterIcon />
                        <span>
                            {selectedMonth === 'all' 
                                ? `${t('showing_all_data') || 'Showing all data'} (${selectedYear})` 
                                : `${t('showing_data_for') || 'Showing data for'} ${getMonthName(parseInt(selectedMonth))} ${selectedYear}`
                            }
                        </span>
                        <Chip 
                            label={`${filteredEarnings.length} ${t('earnings') || 'earnings'}, ${filteredOrders.length} ${t('orders') || 'orders'}`}
                            size="small"
                            color="info"
                        />
                    </Box>
                </Col>
            </Row>

            {/* Earnings DataGrid */}
            <Row>
                <Col>
                    <Box sx={{ height: 400, width: '100%', mb: 4 }}>
                        <DataGrid
                            rows={filteredEarnings}
                            columns={earningsColumns}
                            initialState={{
                                pagination: {
                                    paginationModel: { page: 0, pageSize: 10 },
                                },
                            }}
                            pageSizeOptions={[5, 10, 20]}
                            disableRowSelectionOnClick
                            loading={loading}
                            localeText={{
                                ...getDataGridLocale(),
                                noRowsLabel: selectedMonth === 'all' 
                                    ? t('no_gains_record') 
                                    : t('no_gains_for_selected_period') || 'No gains for selected period'
                            }}
                            sx={{
                                '& .MuiDataGrid-cell': {
                                    borderBottom: '1px solid #e0e0e0',
                                },
                                '& .MuiDataGrid-columnHeaders': {
                                    backgroundColor: '#f5f5f5',
                                    borderBottom: '2px solid #e0e0e0',
                                },
                                '& .MuiDataGrid-row:hover': {
                                    backgroundColor: '#f9f9f9',
                                }
                            }}
                        />
                    </Box>
                </Col>
            </Row>

            {/* Orders Section */}
            <h2 className="mt-3 mb-4">{t('orders')}</h2>
            <Row>
                <Col>
                    <Box sx={{ height: 300, width: '100%' }}>
                        <DataGrid
                            rows={filteredOrders}
                            columns={ordersColumns}
                            initialState={{
                                pagination: {
                                    paginationModel: { page: 0, pageSize: 5 },
                                },
                            }}
                            pageSizeOptions={[5, 10, 20, 50]}
                            disableRowSelectionOnClick
                            loading={loading}
                            localeText={{
                                ...getDataGridLocale(),
                                noRowsLabel: selectedMonth === 'all' 
                                    ? t('no_orders') 
                                    : t('no_orders_for_selected_period') || 'No orders for selected period'
                            }}
                            sx={{
                                '& .MuiDataGrid-cell': {
                                    borderBottom: '1px solid #e0e0e0',
                                },
                                '& .MuiDataGrid-columnHeaders': {
                                    backgroundColor: '#f5f5f5',
                                    borderBottom: '2px solid #e0e0e0',
                                },
                                '& .MuiDataGrid-row:hover': {
                                    backgroundColor: '#f9f9f9',
                                }
                            }}
                        />
                    </Box>
                </Col>
            </Row>
        </Container>
    );
};

export default MyGainsPage;