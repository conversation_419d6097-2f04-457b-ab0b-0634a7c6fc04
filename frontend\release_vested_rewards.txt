CREATE OR REPLACE PROCEDURE public.release_vested_rewards()
LANGUAGE plpgsql
AS $$
DECLARE
  today    date := (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Tokyo')::date;
  r        RECORD;
  audit_id uuid;
BEGIN
  FOR r IN
    WITH due AS (
      SELECT id, user_id, currency_code, total_locked, released_total,
             daily_release_amount, next_release_date,
             CASE
               WHEN next_release_date <= today
                 THEN LEAST(daily_release_amount, total_locked - released_total)
               ELSE 0
             END AS release_amount
      FROM vesting_schedules
      WHERE status = 'active'
        AND next_release_date <= today
    ),
    upd AS (
      UPDATE vesting_schedules vs
      SET released_total = vs.released_total + d.release_amount,
          status = CASE
                     WHEN vs.released_total + d.release_amount >= vs.total_locked
                       THEN 'completed'
                     ELSE 'active'
                   END,
          next_release_date = CASE
                                WHEN vs.released_total + d.release_amount >= vs.total_locked
                                  THEN vs.next_release_date
                                ELSE vs.next_release_date + INTERVAL '1 day'
                              END
      FROM due d
      WHERE vs.id = d.id
      RETURNING d.user_id, d.currency_code, d.id AS schedule_id, d.release_amount
    )
    SELECT user_id, currency_code,
           SUM(release_amount) AS sum_release,
           ARRAY_AGG(schedule_id) AS schedule_ids
    FROM upd
    GROUP BY user_id, currency_code
    HAVING SUM(release_amount) > 0
  LOOP
    UPDATE user_assets ua
    SET balance_locked    = GREATEST(ua.balance_locked - r.sum_release, 0),
        balance_available = ua.balance_available + r.sum_release,
        balance_total     = ua.balance_total
    WHERE ua.user_id = r.user_id
      AND ua.currency_code = r.currency_code;

    IF NOT FOUND THEN
      INSERT INTO user_assets (user_id, currency_code, balance_available, balance_locked, balance_total, withdrawn_total)
      VALUES (r.user_id, r.currency_code, r.sum_release, 0, r.sum_release, 0);
    END IF;

    INSERT INTO audit_logs (id, user_id, action, object_table, object_id, diff, created_at)
    VALUES (
      gen_random_uuid(), r.user_id, 'vesting_release_daily', 'user_assets', r.user_id,
      jsonb_build_object(
        'release_amount', r.sum_release,
        'currency', r.currency_code,
        'schedules', r.schedule_ids
      ),
      now()
    )
    RETURNING id INTO audit_id;

    INSERT INTO transactions (
      id, tx_date, sender_user_id, receiver_user_id, amount_net,
      tx_type, filecoin_msg_id, agent_id, audit_id, created_at
    )
    VALUES (
      gen_random_uuid(), now(), NULL, r.user_id, r.sum_release,
      'vesting_release_daily', NULL, NULL, audit_id, now()
    );
  END LOOP;
END;
$$;
