CREATE OR REPLACE PROCEDURE public.distribute_daily_rewards()
LANGUAGE plpgsql
AS $procedure$
DECLARE
  -- 时间与网络参数
  today date := (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Tokyo')::date;
  latest_fil_per_tib numeric;

  -- 循环订单用
  ord RECORD;

  -- 分发批次与流水
  batch_id uuid;
  order_dist_id uuid;

  -- 用户资产（旧/新）
  old_balance_total numeric := 0;
  old_balance_available numeric := 0;
  new_balance_total numeric;
  new_balance_available numeric;

  -- 交易金额（客户侧）
  tx_amount numeric;          -- 净入账（已扣 total_rate）
  tx_immediate numeric;       -- 立即入账（available）
  tx_locked numeric;          -- 锁仓入账（locked）
  daily_release numeric;      -- 每日释放额（锁仓/天数）

  -- 审计
  audit_id uuid;

  -- 全局配置
  cfg jsonb;
  immediate_pct numeric;      -- 默认 0.25
  vest_days int;              -- 默认 180
BEGIN
  BEGIN
    RAISE NOTICE '--- Start daily reward distribution for % ---', today;

    -- 1) 读取网络单价
    SELECT fil_per_tib INTO latest_fil_per_tib
    FROM network_stats
    ORDER BY stat_date DESC
    LIMIT 1;

    IF latest_fil_per_tib IS NULL THEN
      RAISE NOTICE 'No network_stats found for today (%), aborting.', today;
      RETURN;
    END IF;

    RAISE NOTICE 'Latest fil_per_tib = %', latest_fil_per_tib;

    -- 2) 读取释放配置
    SELECT value INTO cfg FROM app_settings WHERE key = 'rewards_release';
    immediate_pct := COALESCE((cfg->>'immediate_release_pct')::numeric, 0.25);
    vest_days     := COALESCE((cfg->>'vesting_days')::int, 180);

    -- 3) 遍历符合条件的订单（排除 cancelled 客户）
    FOR ord IN
      SELECT
        o.*,
        p.maker_id,
        p.agent_id,              -- 产品维的 agent（用于 batch）
        p.technician_id,
        p.id           AS prod_id,
        p.ops_commission_pct,
        p.commission_agent_pct,
        p.tech_commission_pct,
        cp.agent_id    AS tx_agent_id,            -- 交易归属的代理
        COALESCE(cp.cancelled, FALSE) AS customer_cancelled
      FROM orders o
      JOIN products p
        ON o.product_id = p.id
      LEFT JOIN customer_profiles cp
        ON cp.user_id = o.customer_id
      WHERE o.review_status = 'approved'
        AND (o.start_at + p.effective_delay_days) <= today
        AND o.end_at >= today
        AND COALESCE(cp.cancelled, FALSE) = FALSE     -- ✅ 取消客户不参与
    LOOP
      RAISE NOTICE 'Processing order_id = %', ord.id;

      -- 3.1 避免同日重复处理
      IF EXISTS (
        SELECT 1 FROM order_distributions
        WHERE order_id = ord.id AND created_at::date = today
      ) THEN
        RAISE NOTICE 'Order % already processed today, skipping.', ord.id;
        CONTINUE;
      END IF;

      -- 3.2 查找/创建当日批次
      SELECT id INTO batch_id
      FROM distribution_batches
      WHERE product_id = ord.product_id
        AND created_at::date = today;

      IF batch_id IS NULL THEN
        RAISE NOTICE 'Creating new distribution batch for product_id = %', ord.product_id;
        INSERT INTO distribution_batches (
          id, maker_id, agent_id, currency_code, product_id,
          shares, batch_amount, per_share_amount, status, created_at, distributed_at
        )
        VALUES (
          gen_random_uuid(), ord.maker_id, ord.agent_id, 'FIL', ord.product_id,
          0, 0, latest_fil_per_tib, 'pending', now(), now()
        )
        RETURNING id INTO batch_id;
      ELSE
        RAISE NOTICE 'Using existing distribution batch %', batch_id;
      END IF;

      -- 3.3 记录订单分配项（总额/手续费按未拆分口径）
      order_dist_id := gen_random_uuid();

      INSERT INTO order_distributions (
        id, batch_id, order_id, customer_id, share_amount,
        reward_amount, fee_amount, progress, created_at
      )
      VALUES (
        order_dist_id, batch_id, ord.id, ord.customer_id,
        ord.shares,
        ord.shares * latest_fil_per_tib,                           -- 总奖励
        ord.shares * latest_fil_per_tib * ord.total_rate,          -- 手续费
        1.0, now()
      );

      -- 3.4 更新批次汇总
      UPDATE distribution_batches
      SET
        shares       = shares + ord.shares,
        batch_amount = batch_amount + (ord.shares * latest_fil_per_tib)
      WHERE id = batch_id;

      -- ========= 4) 客户分润：25/75 可配置 =========
      -- 净入账（已扣费率）
      tx_amount := ord.shares * latest_fil_per_tib * (1 - ord.total_rate);

      tx_immediate := tx_amount * immediate_pct;
      tx_locked    := tx_amount - tx_immediate;
      daily_release := CASE WHEN vest_days > 0 THEN (tx_locked / vest_days) ELSE 0 END;

      -- 4.1 读取旧资产（用于审计 diff）
      SELECT balance_total, balance_available
      INTO old_balance_total, old_balance_available
      FROM user_assets
      WHERE user_id = ord.customer_id AND currency_code = 'FIL';

      IF NOT FOUND THEN
        old_balance_total := 0;
        old_balance_available := 0;
      END IF;

      -- 4.2 入账（available += 立即；locked += 锁仓；total += 全部）
      INSERT INTO user_assets (user_id, currency_code, balance_available, balance_locked, balance_total, withdrawn_total)
      VALUES (
        ord.customer_id, 'FIL',
        tx_immediate,
        tx_locked,
        tx_immediate + tx_locked,
        0
      )
      ON CONFLICT (user_id, currency_code)
      DO UPDATE SET
        balance_available = user_assets.balance_available + EXCLUDED.balance_available,
        balance_locked    = user_assets.balance_locked    + EXCLUDED.balance_locked,
        balance_total     = user_assets.balance_total     + EXCLUDED.balance_total;

      new_balance_total     := old_balance_total + tx_immediate + tx_locked;
      new_balance_available := old_balance_available + tx_immediate;

      -- 4.3 审计日志
      audit_id := gen_random_uuid();
      INSERT INTO audit_logs (
        id, user_id, action, object_table, object_id, diff, created_at
      )
      VALUES (
        audit_id,
        ord.customer_id,
        'order_distributions',
        'user_assets',
        ord.customer_id,
        jsonb_build_object(
          'new', jsonb_build_object('user_assets', jsonb_build_object(
            'balance_total', new_balance_total,
            'balance_available', new_balance_available
          )),
          'old', jsonb_build_object('user_assets', jsonb_build_object(
            'balance_total', old_balance_total,
            'balance_available', old_balance_available
          )),
          'split', jsonb_build_object(
            'immediate', tx_immediate,
            'locked', tx_locked,
            'immediate_pct', immediate_pct,
            'vesting_days', vest_days
          )
        ),
        now()
      );

      -- 4.4 交易记录（客户：只记“立即可用”部分）
      INSERT INTO transactions (
        id, tx_date, sender_user_id, receiver_user_id, amount_net,
        tx_type, filecoin_msg_id, agent_id, audit_id, created_at
      )
      VALUES (
        order_dist_id, now(), NULL, ord.customer_id, tx_immediate,
        'order_distributions', NULL, ord.tx_agent_id, audit_id, now()
      );

      -- 4.5 锁仓计划：同一用户+同一开始日+同一天数+status=active 合桶 UPSERT
      IF tx_locked > 0 THEN
        INSERT INTO vesting_schedules (
          user_id, currency_code, source_order_distribution_id,
          total_locked, released_total,
          vesting_start_date, vesting_days, daily_release_amount,
          next_release_date, status
        )
        VALUES (
          ord.customer_id, 'FIL', order_dist_id,
          tx_locked, 0,
          (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Tokyo')::date, vest_days,
          CASE WHEN vest_days > 0 THEN tx_locked / vest_days ELSE 0 END,   -- ✅ 首次插入就给值
          (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Tokyo')::date + INTERVAL '1 day', 'active'
        )
        ON CONFLICT (user_id, currency_code, vesting_start_date, vesting_days, status)
        DO UPDATE SET
          total_locked = vesting_schedules.total_locked + EXCLUDED.total_locked,
          daily_release_amount = CASE
            WHEN vesting_schedules.vesting_days > 0
              THEN (vesting_schedules.total_locked + EXCLUDED.total_locked) / vesting_schedules.vesting_days
            ELSE 0
          END;  -- ✅ 冲突时按“新总额/天数”重算，避免 /0
      END IF;


      -- ========= 5) 角色分润：不锁仓，全部立即发 =========
      DECLARE
        reward_amount numeric := ord.shares * latest_fil_per_tib;  -- 角色基数：总奖励
        maker_reward numeric := reward_amount * ord.ops_commission_pct;
        agent_reward numeric := reward_amount * ord.commission_agent_pct;
        tech_reward  numeric := reward_amount * ord.tech_commission_pct;
        maker_user_id uuid;
        tech_user_id uuid;
      BEGIN
        SELECT user_id INTO maker_user_id FROM maker_profiles WHERE user_id = ord.maker_id;
        SELECT user_id INTO tech_user_id FROM technician_profiles WHERE user_id = ord.technician_id;

        IF maker_user_id IS NOT NULL AND maker_reward > 0 THEN
          RAISE NOTICE 'Distributing maker reward % to user %', maker_reward, maker_user_id;
          PERFORM distribute_bonus(maker_user_id, 'FIL', maker_reward, 'maker_reward', order_dist_id);
        END IF;

        IF ord.agent_id IS NOT NULL AND agent_reward > 0 THEN
          RAISE NOTICE 'Distributing agent reward % to user %', agent_reward, ord.agent_id;
          PERFORM distribute_bonus(ord.agent_id, 'FIL', agent_reward, 'agent_reward', order_dist_id);
        END IF;

        IF tech_user_id IS NOT NULL AND tech_reward > 0 THEN
          RAISE NOTICE 'Distributing technician reward % to user %', tech_reward, tech_user_id;
          PERFORM distribute_bonus(tech_user_id, 'FIL', tech_reward, 'tech_reward', order_dist_id);
        END IF;
      END;

    END LOOP;

    -- 6) 当日 pending 批次 → completed
    RAISE NOTICE 'Marking all pending batches as completed.';
    UPDATE distribution_batches
    SET status = 'completed'
    WHERE status = 'pending'
      AND (created_at AT TIME ZONE 'Asia/Tokyo')::date = today;

    RAISE NOTICE '--- Distribution completed for % ---', today;

  EXCEPTION WHEN OTHERS THEN
    RAISE EXCEPTION '❌ Fatal error in distribute_daily_rewards(): %', SQLERRM;
  END;
END;
$procedure$;
