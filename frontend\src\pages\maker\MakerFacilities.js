import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, Button, Modal, Form, Alert } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import { FaTimes, FaPlus } from 'react-icons/fa';

const MakerFacilities = () => {
    const { t } = useTranslation();
    const [facilities, setFacilities] = useState([]);
    const [loading, setLoading] = useState(true);

    // Edit modal states
    const [showEditModal, setShowEditModal] = useState(false);
    const [editingFacility, setEditingFacility] = useState(null);
    const [editFormData, setEditFormData] = useState({
        name: ''
    });

    // Add modal states
    const [showAddModal, setShowAddModal] = useState(false);
    const [addFormData, setAddFormData] = useState({
        name: ''
    });
    const [addError, setAddError] = useState('');
    const [addSuccess, setAddSuccess] = useState('');
    const [addLoading, setAddLoading] = useState(false);

    // Delete modal states
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [deletingFacility, setDeletingFacility] = useState(null);
    const [deletePassword, setDeletePassword] = useState('');
    const [deleteLoading, setDeleteLoading] = useState(false);

    // Alert states
    const [alert, setAlert] = useState({ show: false, type: '', message: '' });

    useEffect(() => {
        const fetchFacilities = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch facilities associated with products from this maker
            const { data, error } = await supabase
                .from('facilities')
                .select(`
                    id,
                    name,
                    created_at,
                    updated_at
                `)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching facilities:', error);
            } else {
                setFacilities(data);
            }
            setLoading(false);
        };

        fetchFacilities();
    }, []);

    // Handle edit button click
    const handleEditClick = (facility) => {
        setEditingFacility(facility);
        setEditFormData({
            name: facility.name || ''
        });
        setShowEditModal(true);
    };

    // Handle delete button click
    const handleDeleteClick = (facility) => {
        setDeletingFacility(facility);
        setDeletePassword('');
        setShowDeleteModal(true);
    };

    // Handle add button click
    const handleAddClick = () => {
        setAddFormData({ name: '' });
        setAddError('');
        setAddSuccess('');
        setShowAddModal(true);
    };

    // Handle add form submission
    const handleAddSubmit = async (e) => {
        e.preventDefault();
        const supabase = getSupabase();
        if (!supabase) return;

        if (!addFormData.name.trim()) {
            setAddError(t('facility_name_required'));
            return;
        }

        setAddLoading(true);
        setAddError('');
        setAddSuccess('');

        try {
            const { data, error } = await supabase
                .from('facilities')
                .insert({
                    name: addFormData.name.trim()
                })
                .select();

            if (error) throw error;

            // Update local state
            setFacilities([data[0], ...facilities]);
            setAddSuccess(t('facility_added_successfully'));

            // Close modal after 2 seconds
            setTimeout(() => {
                setShowAddModal(false);
                setAddFormData({ name: '' });
                setAddError('');
                setAddSuccess('');
            }, 2000);

        } catch (error) {
            console.error('Error adding facility:', error);
            setAddError(error.message || t('facility_add_error'));
        } finally {
            setAddLoading(false);
        }
    };

    // Close add modal
    const closeAddModal = () => {
        setShowAddModal(false);
        setAddFormData({ name: '' });
        setAddError('');
        setAddSuccess('');
    };

    // Handle edit form submission
    const handleEditSubmit = async (e) => {
        e.preventDefault();
        const supabase = getSupabase();
        if (!supabase || !editingFacility) return;

        try {
            const { error } = await supabase
                .from('facilities')
                .update({
                    name: editFormData.name,
                    updated_at: new Date().toISOString()
                })
                .eq('id', editingFacility.id);

            if (error) throw error;

            // Update local state
            setFacilities(facilities.map(facility =>
                facility.id === editingFacility.id
                    ? { ...facility, ...editFormData, updated_at: new Date().toISOString() }
                    : facility
            ));

            setAlert({ show: true, type: 'success', message: t('item_updated_successfully') });
            setShowEditModal(false);
            setEditingFacility(null);
        } catch (error) {
            console.error('Error updating facility:', error);
            setAlert({ show: true, type: 'danger', message: t('failed_to_update_item') + ': ' + error.message });
        }
    };

    // Handle delete confirmation
    const handleDeleteConfirm = async () => {
        const supabase = getSupabase();
        if (!supabase || !deletingFacility) return;

        // Validate password input
        if (!deletePassword.trim()) {
            setAlert({ show: true, type: 'danger', message: t('password_required') });
            return;
        }

        setDeleteLoading(true);

        try {
            // Get current user
            const { data: { user }, error: userError } = await supabase.auth.getUser();
            if (userError || !user) {
                throw new Error(t('user_not_authenticated'));
            }

            // Verify password by attempting to sign in
            const { error: signInError } = await supabase.auth.signInWithPassword({
                email: user.email,
                password: deletePassword,
            });

            if (signInError) {
                throw new Error(t('password_incorrect'));
            }

            // Proceed with deletion
            const { error } = await supabase
                .from('facilities')
                .delete()
                .eq('id', deletingFacility.id);

            if (error) throw error;

            // Update local state
            setFacilities(facilities.filter(facility => facility.id !== deletingFacility.id));

            setAlert({ show: true, type: 'success', message: t('item_deleted_successfully') });
            setShowDeleteModal(false);
            setDeletingFacility(null);
            setDeletePassword('');
        } catch (error) {
            console.error('Error deleting facility:', error);
            setAlert({ show: true, type: 'danger', message: error.message || t('failed_to_delete_item') });
        } finally {
            setDeleteLoading(false);
        }
    };

    // Handle form input changes
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setEditFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    // Close alert
    const closeAlert = () => {
        setAlert({ show: false, type: '', message: '' });
    };

    if (loading) {
        return <div>{t('loading_facilities')}</div>;
    }

    return (
        <Container>
            {alert.show && (
                <Alert variant={alert.type} dismissible onClose={closeAlert} className="mb-4">
                    {alert.message}
                </Alert>
            )}
            <h2 className="mb-4">{t('all_facilities')}</h2>

            {/* Top Operation Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={2}>
                                    <Button
                                        variant="primary"
                                        onClick={handleAddClick}
                                        className="mb-2"
                                    >
                                        <FaPlus className="me-1" />
                                        {t('add_facility')}
                                    </Button>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>{t('name')}</th>
                                        <th>{t('created_at')}</th>
                                        <th>{t('updated_at')}</th>
                                        <th>{t('actions')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {facilities.length === 0 ? (
                                        <tr>
                                            <td colSpan="9" className="text-center">{t('no_facilities_available')}</td>
                                        </tr>
                                    ) : (
                                        facilities.map(facility => (
                                            <tr key={facility.id}>
                                                <td>{facility.id}</td>
                                                <td>{facility.name}</td>
                                                <td>{new Date(facility.created_at).toLocaleString()}</td>
                                                <td>
                                                    {facility.updated_at && !isNaN(new Date(facility.updated_at))
                                                        ? new Date(facility.updated_at).toLocaleString()
                                                        : '–'}
                                                </td>
                                                <td>
                                                    <Button
                                                        variant="info"
                                                        size="sm"
                                                        className="me-2"
                                                        onClick={() => handleEditClick(facility)}
                                                    >
                                                        {t('edit')}
                                                    </Button>
                                                    <Button
                                                        variant="danger"
                                                        size="sm"
                                                        onClick={() => handleDeleteClick(facility)}
                                                    >
                                                        {t('delete')}
                                                    </Button>
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Edit Modal */}
            <Modal show={showEditModal} onHide={() => setShowEditModal(false)} size="lg">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('edit_facility')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={() => setShowEditModal(false)}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    <Form onSubmit={handleEditSubmit}>
                        <Form.Group className="mb-3">
                            <Form.Label>{t('name')}</Form.Label>
                            <Form.Control
                                type="text"
                                name="name"
                                value={editFormData.name}
                                onChange={handleInputChange}
                                required
                            />
                        </Form.Group>
                        <div className="d-flex justify-content-end">
                            <Button variant="secondary" className="me-2" onClick={() => setShowEditModal(false)}>
                                {t('cancel')}
                            </Button>
                            <Button variant="primary" type="submit">
                                {t('save_changes')}
                            </Button>
                        </div>
                    </Form>
                </Modal.Body>
            </Modal>

            {/* Add Facility Modal */}
            <Modal show={showAddModal} onHide={closeAddModal} size="md">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('add_facility')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeAddModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {addError && (
                        <Alert variant="danger" className="mb-3">
                            {addError}
                        </Alert>
                    )}
                    {addSuccess && (
                        <Alert variant="success" className="mb-3">
                            {addSuccess}
                        </Alert>
                    )}

                    <Form onSubmit={handleAddSubmit}>
                        <Form.Group className="mb-3">
                            <Form.Label><strong>{t('facility_name')}</strong></Form.Label>
                            <Form.Control
                                type="text"
                                value={addFormData.name}
                                onChange={(e) => setAddFormData({ ...addFormData, name: e.target.value })}
                                placeholder={t('enter_facility_name')}
                                disabled={addLoading}
                                required
                            />
                            <Form.Text className="text-muted">
                                {t('facility_name_help')}
                            </Form.Text>
                        </Form.Group>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeAddModal} disabled={addLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleAddSubmit}
                        disabled={addLoading || !addFormData.name.trim()}
                    >
                        {addLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('creating')}
                            </>
                        ) : (
                            <>
                                <FaPlus className="me-1" />
                                {t('create_facility')}
                            </>
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Delete Confirmation Modal */}
            <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('confirm_delete')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={() => setShowDeleteModal(false)}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    <p>{t('delete_confirmation')}</p>
                    {deletingFacility && (
                        <p><strong>{t('name')}: {deletingFacility.name}</strong></p>
                    )}
                    <Form.Group className="mt-3">
                        <Form.Label>{t('enter_login_password')}</Form.Label>
                        <Form.Control
                            type="password"
                            value={deletePassword}
                            onChange={(e) => setDeletePassword(e.target.value)}
                            placeholder={t('enter_login_password')}
                            required
                        />
                        <Form.Text className="text-muted">
                            {t('password_confirmation_required')}
                        </Form.Text>
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="danger"
                        onClick={handleDeleteConfirm}
                        disabled={deleteLoading}
                    >
                        {deleteLoading ? t('deleting') : t('confirm')}
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default MakerFacilities;
