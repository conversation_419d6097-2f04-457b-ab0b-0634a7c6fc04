--
-- PostgreSQL database dump
--

-- Dumped from database version 15.8
-- Dumped by pg_dump version 15.8

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA public;


--
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON SCHEMA public IS 'standard public schema';


--
-- Name: current_user_role(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.current_user_role() RETURNS text
    LANGUAGE sql STABLE PARALLEL SAFE
    AS $$
  SELECT role FROM users WHERE id = auth.uid();
$$;


--
-- Name: distribute_bonus(uuid, text, numeric, text, uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.distribute_bonus(p_user_id uuid, p_currency_code text, p_amount numeric, p_tx_type text, p_order_dist_id uuid) RETURNS void
    LANGUAGE plpgsql
    AS $$
DECLARE
  v_order_id       uuid;
  v_agent_id       uuid;
  v_old_total      numeric := 0;
  v_old_available  numeric := 0;
  v_new_total      numeric;
  v_new_available  numeric;
  v_audit_id       uuid;
BEGIN
  IF COALESCE(p_amount, 0) <= 0 THEN
    RETURN;
  END IF;

  -- 查 order_id 与 agent
  SELECT o.id, cp.agent_id, pr.agent_id
  INTO v_order_id, v_agent_id, v_agent_id
  FROM order_distributions od
  JOIN orders o ON o.id = od.order_id
  LEFT JOIN customer_profiles cp ON cp.user_id = o.customer_id
  LEFT JOIN products pr ON pr.id = o.product_id
  WHERE od.id = p_order_dist_id
  LIMIT 1;

  -- 归属 agent：优先客户代理，其次产品代理
  v_agent_id := COALESCE(v_agent_id, v_agent_id);

  -- 旧资产
  SELECT balance_total, balance_available
  INTO v_old_total, v_old_available
  FROM user_assets
  WHERE user_id = p_user_id AND currency_code = p_currency_code;

  IF NOT FOUND THEN
    v_old_total := 0;
    v_old_available := 0;
  END IF;

  -- 入账
  INSERT INTO user_assets (user_id, currency_code, balance_available, balance_locked, balance_total, withdrawn_total)
  VALUES (p_user_id, p_currency_code, p_amount, 0, p_amount, 0)
  ON CONFLICT (user_id, currency_code)
  DO UPDATE SET
    balance_available = user_assets.balance_available + EXCLUDED.balance_available,
    balance_total     = user_assets.balance_total     + EXCLUDED.balance_total;

  v_new_total     := v_old_total + p_amount;
  v_new_available := v_old_available + p_amount;

  -- 审计
  v_audit_id := gen_random_uuid();
  INSERT INTO audit_logs (id, user_id, action, object_table, object_id, diff, created_at)
  VALUES (
    v_audit_id, p_user_id, p_tx_type, 'user_assets', p_user_id,
    jsonb_build_object(
      'order_dist_id', p_order_dist_id,
      'order_id',      v_order_id,
      'type',          p_tx_type,
      'amount',        p_amount,
      'currency',      p_currency_code,
      'old', jsonb_build_object(
        'balance_total',     v_old_total,
        'balance_available', v_old_available
      ),
      'new', jsonb_build_object(
        'balance_total',     v_new_total,
        'balance_available', v_new_available
      )
    ),
    now()
  );

  -- 交易
  INSERT INTO transactions (
    id, tx_date, sender_user_id, receiver_user_id, amount_net,
    tx_type, filecoin_msg_id, agent_id, audit_id, order_id, created_at
  )
  VALUES (
    gen_random_uuid(), now(), NULL, p_user_id, p_amount,
    p_tx_type, NULL, v_agent_id, v_audit_id, v_order_id, now()
  );

  RETURN;
END;
$$;


--
-- Name: distribute_daily_rewards(); Type: PROCEDURE; Schema: public; Owner: -
--

CREATE PROCEDURE public.distribute_daily_rewards()
    LANGUAGE plpgsql
    AS $$
DECLARE
  -- 时间与网络参数
  today date := (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Tokyo')::date;
  latest_fil_per_tib numeric;

  -- 循环订单用
  ord RECORD;

  -- 分发批次与流水
  batch_id uuid;
  order_dist_id uuid;

  -- 用户资产（旧/新）
  old_balance_total numeric := 0;
  old_balance_available numeric := 0;
  new_balance_total numeric;
  new_balance_available numeric;

  -- 交易金额（客户侧）
  tx_amount numeric;          -- 净入账（已扣 total_rate）
  tx_immediate numeric;       -- 立即入账（available）
  tx_locked numeric;          -- 锁仓入账（locked）
  daily_release numeric;      -- 每日释放额（锁仓/天数）

  -- 审计
  audit_id uuid;

  -- 全局配置
  cfg jsonb;
  immediate_pct numeric;      -- 默认 0.25
  vest_days int;              -- 默认 180
BEGIN
  BEGIN
    RAISE NOTICE '--- Start daily reward distribution for % ---', today;

    -- 1) 读取网络单价
    SELECT fil_per_tib INTO latest_fil_per_tib
    FROM network_stats
    ORDER BY stat_date DESC
    LIMIT 1;

    IF latest_fil_per_tib IS NULL THEN
      RAISE NOTICE 'No network_stats found for today (%), aborting.', today;
      RETURN;
    END IF;

    RAISE NOTICE 'Latest fil_per_tib = %', latest_fil_per_tib;

    -- 2) 读取释放配置
    SELECT value INTO cfg FROM app_settings WHERE key = 'rewards_release';
    immediate_pct := COALESCE((cfg->>'immediate_release_pct')::numeric, 0.25);
    vest_days     := COALESCE((cfg->>'vesting_days')::int, 180);

    -- 3) 遍历符合条件的订单（排除 cancelled 客户）
    FOR ord IN
      SELECT
        o.*,
        p.maker_id,
        p.agent_id,              -- 产品维的 agent（用于 batch）
        p.technician_id,
        p.id           AS prod_id,
        p.ops_commission_pct,
        p.commission_agent_pct,
        p.tech_commission_pct,
        cp.agent_id    AS tx_agent_id,            -- 交易归属的代理
        COALESCE(cp.cancelled, FALSE) AS customer_cancelled
      FROM orders o
      JOIN products p
        ON o.product_id = p.id
      LEFT JOIN customer_profiles cp
        ON cp.user_id = o.customer_id
      WHERE o.review_status = 'approved'
        AND (o.start_at + p.effective_delay_days) <= today
        AND o.end_at >= today
        AND COALESCE(cp.cancelled, FALSE) = FALSE     -- ✅ 取消客户不参与
    LOOP
      RAISE NOTICE 'Processing order_id = %', ord.id;

      -- 3.1 避免同日重复处理
      IF EXISTS (
        SELECT 1 FROM order_distributions
        WHERE order_id = ord.id AND created_at::date = today
      ) THEN
        RAISE NOTICE 'Order % already processed today, skipping.', ord.id;
        CONTINUE;
      END IF;

      -- 3.2 查找/创建当日批次
      SELECT id INTO batch_id
      FROM distribution_batches
      WHERE product_id = ord.product_id
        AND created_at::date = today;

      IF batch_id IS NULL THEN
        RAISE NOTICE 'Creating new distribution batch for product_id = %', ord.product_id;
        INSERT INTO distribution_batches (
          id, maker_id, agent_id, currency_code, product_id,
          shares, batch_amount, per_share_amount, status, created_at, distributed_at
        )
        VALUES (
          gen_random_uuid(), ord.maker_id, ord.agent_id, 'FIL', ord.product_id,
          0, 0, latest_fil_per_tib, 'pending', now(), now()
        )
        RETURNING id INTO batch_id;
      ELSE
        RAISE NOTICE 'Using existing distribution batch %', batch_id;
      END IF;

      -- 3.3 记录订单分配项（总额/手续费按未拆分口径）
      order_dist_id := gen_random_uuid();

      INSERT INTO order_distributions (
        id, batch_id, order_id, customer_id, share_amount,
        reward_amount, fee_amount, progress, created_at
      )
      VALUES (
        order_dist_id, batch_id, ord.id, ord.customer_id,
        ord.shares,
        ord.shares * latest_fil_per_tib,                           -- 总奖励
        ord.shares * latest_fil_per_tib * ord.total_rate,          -- 手续费
        1.0, now()
      );

      -- 3.4 更新批次汇总
      UPDATE distribution_batches
      SET
        shares       = shares + ord.shares,
        batch_amount = batch_amount + (ord.shares * latest_fil_per_tib)
      WHERE id = batch_id;

      -- ========= 4) 客户分润：25/75 可配置 =========
      -- 净入账（已扣费率）
      tx_amount := ord.shares * latest_fil_per_tib * (1 - ord.total_rate);

      tx_immediate := tx_amount * immediate_pct;
      tx_locked    := tx_amount - tx_immediate;
      daily_release := CASE WHEN vest_days > 0 THEN (tx_locked / vest_days) ELSE 0 END;

      -- 4.1 读取旧资产（用于审计 diff）
      SELECT balance_total, balance_available
      INTO old_balance_total, old_balance_available
      FROM user_assets
      WHERE user_id = ord.customer_id AND currency_code = 'FIL';

      IF NOT FOUND THEN
        old_balance_total := 0;
        old_balance_available := 0;
      END IF;

      -- 4.2 入账（available += 立即；locked += 锁仓；total += 全部）
      INSERT INTO user_assets (user_id, currency_code, balance_available, balance_locked, balance_total, withdrawn_total)
      VALUES (
        ord.customer_id, 'FIL',
        tx_immediate,
        tx_locked,
        tx_immediate + tx_locked,
        0
      )
      ON CONFLICT (user_id, currency_code)
      DO UPDATE SET
        balance_available = user_assets.balance_available + EXCLUDED.balance_available,
        balance_locked    = user_assets.balance_locked    + EXCLUDED.balance_locked,
        balance_total     = user_assets.balance_total     + EXCLUDED.balance_total;

      new_balance_total     := old_balance_total + tx_immediate + tx_locked;
      new_balance_available := old_balance_available + tx_immediate;

      -- 4.3 审计日志
      audit_id := gen_random_uuid();
      INSERT INTO audit_logs (
        id, user_id, action, object_table, object_id, diff, created_at
      )
      VALUES (
        audit_id,
        ord.customer_id,
        'order_distributions',
        'user_assets',
        ord.customer_id,
        jsonb_build_object(
          'new', jsonb_build_object('user_assets', jsonb_build_object(
            'balance_total', new_balance_total,
            'balance_available', new_balance_available
          )),
          'old', jsonb_build_object('user_assets', jsonb_build_object(
            'balance_total', old_balance_total,
            'balance_available', old_balance_available
          )),
          'split', jsonb_build_object(
            'immediate', tx_immediate,
            'locked', tx_locked,
            'immediate_pct', immediate_pct,
            'vesting_days', vest_days
          )
        ),
        now()
      );

      -- 4.4 交易记录（客户：只记“立即可用”部分）
      INSERT INTO transactions (
        id, tx_date, sender_user_id, receiver_user_id, amount_net,
        tx_type, filecoin_msg_id, agent_id, audit_id, order_id, created_at
      )
      VALUES (
        order_dist_id, now(), NULL, ord.customer_id, tx_immediate,
        'order_distributions', NULL, ord.tx_agent_id, audit_id, ord.id, now()
      );

      -- 4.5 锁仓计划：同一用户+同一开始日+同一天数+status=active 合桶 UPSERT
      IF tx_locked > 0 THEN
        INSERT INTO vesting_schedules (
          user_id, currency_code, source_order_distribution_id,
          total_locked, released_total,
          vesting_start_date, vesting_days, daily_release_amount,
          next_release_date, status
        )
        VALUES (
          ord.customer_id, 'FIL', order_dist_id,
          tx_locked, 0,
          (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Tokyo')::date, vest_days,
          CASE WHEN vest_days > 0 THEN tx_locked / vest_days ELSE 0 END,   -- ✅ 首次插入就给值
          (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Tokyo')::date + INTERVAL '1 day', 'active'
        )
        ON CONFLICT (user_id, currency_code, vesting_start_date, vesting_days, status)
        DO UPDATE SET
          total_locked = vesting_schedules.total_locked + EXCLUDED.total_locked,
          daily_release_amount = CASE
            WHEN vesting_schedules.vesting_days > 0
              THEN (vesting_schedules.total_locked + EXCLUDED.total_locked) / vesting_schedules.vesting_days
            ELSE 0
          END;  -- ✅ 冲突时按“新总额/天数”重算，避免 /0
      END IF;


      -- ========= 5) 角色分润：不锁仓，全部立即发 =========
      DECLARE
        reward_amount numeric := ord.shares * latest_fil_per_tib;  -- 角色基数：总奖励
        maker_reward numeric := reward_amount * ord.ops_commission_pct;
        agent_reward numeric := reward_amount * ord.commission_agent_pct;
        tech_reward  numeric := reward_amount * ord.tech_commission_pct;
        maker_user_id uuid;
        tech_user_id uuid;
      BEGIN
        SELECT user_id INTO maker_user_id FROM maker_profiles WHERE user_id = ord.maker_id;
        SELECT user_id INTO tech_user_id FROM technician_profiles WHERE user_id = ord.technician_id;

        IF maker_user_id IS NOT NULL AND maker_reward > 0 THEN
          RAISE NOTICE 'Distributing maker reward % to user %', maker_reward, maker_user_id;
          PERFORM distribute_bonus(maker_user_id, 'FIL', maker_reward, 'maker_reward', order_dist_id);
        END IF;

        IF ord.agent_id IS NOT NULL AND agent_reward > 0 THEN
          RAISE NOTICE 'Distributing agent reward % to user %', agent_reward, ord.agent_id;
          PERFORM distribute_bonus(ord.agent_id, 'FIL', agent_reward, 'agent_reward', order_dist_id);
        END IF;

        IF tech_user_id IS NOT NULL AND tech_reward > 0 THEN
          RAISE NOTICE 'Distributing technician reward % to user %', tech_reward, tech_user_id;
          PERFORM distribute_bonus(tech_user_id, 'FIL', tech_reward, 'tech_reward', order_dist_id);
        END IF;
      END;

    END LOOP;

    -- 6) 当日 pending 批次 → completed
    RAISE NOTICE 'Marking all pending batches as completed.';
    UPDATE distribution_batches
    SET status = 'completed'
    WHERE status = 'pending'
      AND (created_at AT TIME ZONE 'Asia/Tokyo')::date = today;

    RAISE NOTICE '--- Distribution completed for % ---', today;

  EXCEPTION WHEN OTHERS THEN
    RAISE EXCEPTION '❌ Fatal error in distribute_daily_rewards(): %', SQLERRM;
  END;
END;
$$;


--
-- Name: release_vested_rewards(); Type: PROCEDURE; Schema: public; Owner: -
--

CREATE PROCEDURE public.release_vested_rewards()
    LANGUAGE plpgsql
    AS $$
DECLARE
  today    date := (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Tokyo')::date;
  r        RECORD;
  audit_id uuid;
BEGIN
  FOR r IN
    WITH due AS (
      SELECT
        id,
        user_id,
        currency_code,
        total_locked,
        released_total,
        daily_release_amount,
        next_release_date,
        CASE
          WHEN next_release_date <= today
            THEN LEAST(daily_release_amount, total_locked - released_total)
          ELSE 0
        END AS release_amount
      FROM vesting_schedules
      WHERE status = 'active'
        AND next_release_date <= today
    )
    UPDATE vesting_schedules vs
    SET released_total = vs.released_total + d.release_amount,
        status = CASE
                   WHEN vs.released_total + d.release_amount >= vs.total_locked
                     THEN 'completed'
                   ELSE 'active'
                 END,
        next_release_date = CASE
                              WHEN vs.released_total + d.release_amount >= vs.total_locked
                                THEN vs.next_release_date
                              ELSE vs.next_release_date + INTERVAL '1 day'
                            END
    FROM due d
    LEFT JOIN order_distributions od
      ON od.id = vs.source_order_distribution_id
    WHERE vs.id = d.id
    RETURNING
      vs.id                              AS schedule_id,
      vs.user_id,
      vs.currency_code,
      d.release_amount,
      vs.source_order_distribution_id    AS order_dist_id,
      od.order_id                        AS order_id
  LOOP
    IF COALESCE(r.release_amount, 0) <= 0 THEN
      CONTINUE;
    END IF;

    -- 1) 资产更新
    UPDATE user_assets ua
    SET balance_locked    = GREATEST(ua.balance_locked - r.release_amount, 0),
        balance_available = ua.balance_available + r.release_amount,
        balance_total     = ua.balance_total
    WHERE ua.user_id = r.user_id
      AND ua.currency_code = r.currency_code;

    IF NOT FOUND THEN
      INSERT INTO user_assets (user_id, currency_code, balance_available, balance_locked, balance_total, withdrawn_total)
      VALUES (r.user_id, r.currency_code, r.release_amount, 0, r.release_amount, 0);
    END IF;

    -- 2) 审计
    INSERT INTO audit_logs (id, user_id, action, object_table, object_id, diff, created_at)
    VALUES (
      gen_random_uuid(), r.user_id, 'vesting_release_daily', 'user_assets', r.user_id,
      jsonb_build_object(
        'release_amount', r.release_amount,
        'currency',       r.currency_code,
        'schedule_id',    r.schedule_id,
        'order_dist_id',  r.order_dist_id,
        'order_id',       r.order_id
      ),
      now()
    )
    RETURNING id INTO audit_id;

    -- 3) 交易（按订单的锁仓计划每天一条）
    INSERT INTO transactions (
      id, tx_date, sender_user_id, receiver_user_id, amount_net,
      tx_type, filecoin_msg_id, agent_id, audit_id, order_id, created_at
    )
    VALUES (
      gen_random_uuid(), now(), NULL, r.user_id, r.release_amount,
      'vesting_release_daily', NULL, NULL, audit_id, r.order_id, now()
    );
  END LOOP;
END;
$$;


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: agent_profiles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.agent_profiles (
    user_id uuid NOT NULL,
    maker_id uuid,
    brand_name character varying,
    commission_pct numeric,
    created_at timestamp with time zone
);


--
-- Name: app_settings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.app_settings (
    key text NOT NULL,
    value jsonb NOT NULL
);


--
-- Name: audit_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.audit_logs (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid,
    action character varying,
    object_table character varying,
    object_id uuid,
    diff jsonb,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: capacity_requests; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.capacity_requests (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    maker_id uuid,
    product_id uuid NOT NULL,
    added_capacity numeric,
    capacity_before numeric,
    capacity_after numeric,
    requested_by uuid,
    status character varying,
    description text,
    review_reply text,
    requested_at timestamp with time zone DEFAULT now() NOT NULL,
    reviewed_at timestamp with time zone
);


--
-- Name: currencies; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.currencies (
    code character varying NOT NULL,
    total_supply numeric,
    withdrawable numeric
);


--
-- Name: customer_profiles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.customer_profiles (
    user_id uuid NOT NULL,
    agent_id uuid,
    real_name character varying,
    id_number character varying,
    id_img_front character varying,
    id_img_back character varying,
    verify_status character varying,
    withdraw_pwd_hash character varying,
    created_at timestamp with time zone,
    email_confirmed boolean,
    status bigint,
    cancelled boolean DEFAULT false NOT NULL,
    kyc_submitted_at timestamp with time zone
);


--
-- Name: distribution_batches; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.distribution_batches (
    id uuid NOT NULL,
    maker_id uuid,
    agent_id uuid,
    currency_code character varying,
    product_id uuid,
    shares numeric,
    batch_amount numeric,
    per_share_amount numeric,
    status character varying,
    created_at timestamp with time zone NOT NULL,
    distributed_at timestamp with time zone
);


--
-- Name: documents_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.documents_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: facilities; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.facilities (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying,
    created_at timestamp with time zone,
    updated_at timestamp with time zone
);


--
-- Name: maker_profiles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.maker_profiles (
    user_id uuid NOT NULL,
    domain character varying,
    logo_url character varying,
    support_email character varying,
    sms_signature character varying,
    enable_agent_order boolean DEFAULT true,
    enable_product_create boolean DEFAULT true,
    created_at timestamp with time zone
);


--
-- Name: manual_journals; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.manual_journals (
    id uuid NOT NULL,
    maker_id uuid,
    customer_id uuid,
    currency_code character varying,
    amount numeric,
    journal_type character varying,
    remark text,
    created_at timestamp with time zone
);


--
-- Name: microsoft_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.microsoft_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: migrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.migrations (
    id integer NOT NULL,
    name text NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: migrations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.migrations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: migrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.migrations_id_seq OWNED BY public.migrations.id;


--
-- Name: miner_daily_earnings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.miner_daily_earnings (
    miner_id uuid NOT NULL,
    earn_date date NOT NULL,
    cumulative_reward numeric,
    daily_reward numeric,
    blocks_won integer,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: miner_daily_snapshots; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.miner_daily_snapshots (
    miner_id uuid NOT NULL,
    snapshot_date date NOT NULL,
    blockchain_height bigint,
    power numeric,
    available_balance numeric,
    pledge_locked numeric,
    balance numeric
);


--
-- Name: miners; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.miners (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    category character varying,
    facility_id uuid,
    filecoin_miner_id character varying,
    sector_size bigint,
    effective_until date,
    created_at timestamp with time zone,
    updated_at timestamp with time zone
);


--
-- Name: network_stats; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.network_stats (
    stat_date timestamp with time zone DEFAULT now() NOT NULL,
    fil_per_tib numeric
);


--
-- Name: order_distributions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_distributions (
    id uuid NOT NULL,
    batch_id uuid,
    order_id uuid,
    customer_id uuid,
    share_amount numeric,
    reward_amount numeric,
    fee_amount numeric,
    progress numeric,
    created_at timestamp with time zone NOT NULL
);


--
-- Name: orders; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.orders (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    product_id uuid,
    agent_id uuid,
    customer_id uuid,
    shares numeric,
    proof_image_url character varying,
    storage_cost numeric,
    pledge_cost numeric,
    total_rate numeric,
    tech_fee_pct numeric,
    sales_fee_pct numeric,
    ops_fee_pct numeric,
    start_at date,
    end_at date,
    review_status character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone,
    deleted_at timestamp with time zone
);


--
-- Name: products; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.products (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    maker_id uuid,
    category character varying,
    name character varying,
    total_shares numeric,
    miner_id uuid,
    price numeric,
    effective_delay_days integer,
    min_purchase numeric,
    sold_shares numeric,
    partner_reward_pct numeric,
    ops_commission_pct numeric,
    tech_commission_pct numeric,
    commission_agent_pct numeric,
    is_disabled boolean DEFAULT false,
    self_distribution boolean,
    auto_distribution boolean,
    review_status character varying,
    created_at timestamp with time zone DEFAULT now(),
    delisted_at timestamp with time zone,
    agent_id uuid,
    duration_days integer DEFAULT 1 NOT NULL,
    technician_id uuid
);


--
-- Name: technician_profiles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.technician_profiles (
    user_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    maker_id uuid
);


--
-- Name: transactions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.transactions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    tx_date timestamp with time zone DEFAULT now() NOT NULL,
    sender_user_id uuid,
    receiver_user_id uuid,
    amount_net numeric,
    tx_type character varying,
    filecoin_msg_id character varying,
    agent_id uuid,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    audit_id uuid,
    order_id uuid
);


--
-- Name: user_assets; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_assets (
    user_id uuid NOT NULL,
    currency_code character varying NOT NULL,
    balance_available numeric,
    balance_locked numeric,
    balance_total numeric,
    withdrawn_total numeric,
    created_at timestamp with time zone
);


--
-- Name: TABLE user_assets; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.user_assets IS 'Trigger schema cache refresh';


--
-- Name: users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.users (
    id uuid NOT NULL,
    email character varying,
    phone character varying,
    role character varying,
    invite_code character varying NOT NULL,
    referred_by uuid,
    created_at timestamp with time zone
);


--
-- Name: vesting_schedules; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.vesting_schedules (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    currency_code text DEFAULT 'FIL'::text NOT NULL,
    source_order_distribution_id uuid NOT NULL,
    total_locked numeric NOT NULL,
    released_total numeric DEFAULT 0 NOT NULL,
    vesting_start_date date NOT NULL,
    vesting_days integer NOT NULL,
    daily_release_amount numeric DEFAULT '0'::numeric NOT NULL,
    next_release_date date NOT NULL,
    status text DEFAULT 'active'::text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: withdrawals; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.withdrawals (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid,
    currency_code character varying,
    request_amount numeric,
    final_amount numeric,
    fee numeric,
    user_remark text,
    admin_remark text,
    status character varying,
    requested_at timestamp with time zone DEFAULT now() NOT NULL,
    reviewed_at timestamp with time zone,
    wallet_type character varying,
    address character varying
);


--
-- Name: migrations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migrations ALTER COLUMN id SET DEFAULT nextval('public.migrations_id_seq'::regclass);


--
-- Name: agent_profiles agent_profiles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.agent_profiles
    ADD CONSTRAINT agent_profiles_pkey PRIMARY KEY (user_id);


--
-- Name: app_settings app_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.app_settings
    ADD CONSTRAINT app_settings_pkey PRIMARY KEY (key);


--
-- Name: audit_logs audit_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT audit_logs_pkey PRIMARY KEY (id);


--
-- Name: capacity_requests capacity_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.capacity_requests
    ADD CONSTRAINT capacity_requests_pkey PRIMARY KEY (id);


--
-- Name: currencies currencies_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.currencies
    ADD CONSTRAINT currencies_pkey PRIMARY KEY (code);


--
-- Name: customer_profiles customer_profiles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_profiles
    ADD CONSTRAINT customer_profiles_pkey PRIMARY KEY (user_id);


--
-- Name: distribution_batches distribution_batches_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.distribution_batches
    ADD CONSTRAINT distribution_batches_pkey PRIMARY KEY (id);


--
-- Name: facilities facilities_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.facilities
    ADD CONSTRAINT facilities_pkey PRIMARY KEY (id);


--
-- Name: maker_profiles maker_profiles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.maker_profiles
    ADD CONSTRAINT maker_profiles_pkey PRIMARY KEY (user_id);


--
-- Name: manual_journals manual_journals_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.manual_journals
    ADD CONSTRAINT manual_journals_pkey PRIMARY KEY (id);


--
-- Name: migrations migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migrations
    ADD CONSTRAINT migrations_pkey PRIMARY KEY (id);


--
-- Name: miner_daily_earnings miner_daily_earnings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.miner_daily_earnings
    ADD CONSTRAINT miner_daily_earnings_pkey PRIMARY KEY (miner_id, earn_date);


--
-- Name: miner_daily_snapshots miner_daily_snapshots_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.miner_daily_snapshots
    ADD CONSTRAINT miner_daily_snapshots_pkey PRIMARY KEY (miner_id, snapshot_date);


--
-- Name: miners miners_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.miners
    ADD CONSTRAINT miners_pkey PRIMARY KEY (id);


--
-- Name: network_stats network_stats_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.network_stats
    ADD CONSTRAINT network_stats_pkey PRIMARY KEY (stat_date);


--
-- Name: order_distributions order_distributions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_distributions
    ADD CONSTRAINT order_distributions_pkey PRIMARY KEY (id);


--
-- Name: orders orders_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_pkey PRIMARY KEY (id);


--
-- Name: products products_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.products
    ADD CONSTRAINT products_pkey PRIMARY KEY (id);


--
-- Name: technician_profiles technician_profiles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.technician_profiles
    ADD CONSTRAINT technician_profiles_pkey PRIMARY KEY (user_id);


--
-- Name: transactions transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.transactions
    ADD CONSTRAINT transactions_pkey PRIMARY KEY (id);


--
-- Name: vesting_schedules uniq_vesting_bucket; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.vesting_schedules
    ADD CONSTRAINT uniq_vesting_bucket UNIQUE (user_id, currency_code, vesting_start_date, vesting_days, status);


--
-- Name: user_assets user_assets_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_assets
    ADD CONSTRAINT user_assets_pkey PRIMARY KEY (user_id, currency_code);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_invite_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_invite_code_key UNIQUE (invite_code);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: vesting_schedules vesting_schedules_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.vesting_schedules
    ADD CONSTRAINT vesting_schedules_pkey PRIMARY KEY (id);


--
-- Name: withdrawals withdrawals_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.withdrawals
    ADD CONSTRAINT withdrawals_pkey PRIMARY KEY (id);


--
-- Name: idx_vesting_user_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_vesting_user_status ON public.vesting_schedules USING btree (user_id, status);


--
-- Name: agent_profiles agent_profiles_maker_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.agent_profiles
    ADD CONSTRAINT agent_profiles_maker_id_fkey FOREIGN KEY (maker_id) REFERENCES public.maker_profiles(user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: agent_profiles agent_profiles_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.agent_profiles
    ADD CONSTRAINT agent_profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: audit_logs audit_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT audit_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: capacity_requests capacity_requests_maker_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.capacity_requests
    ADD CONSTRAINT capacity_requests_maker_id_fkey FOREIGN KEY (maker_id) REFERENCES public.maker_profiles(user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: capacity_requests capacity_requests_product_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.capacity_requests
    ADD CONSTRAINT capacity_requests_product_id_fkey FOREIGN KEY (product_id) REFERENCES public.products(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: capacity_requests capacity_requests_requested_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.capacity_requests
    ADD CONSTRAINT capacity_requests_requested_by_fkey FOREIGN KEY (requested_by) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: customer_profiles customer_profiles_agent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_profiles
    ADD CONSTRAINT customer_profiles_agent_id_fkey FOREIGN KEY (agent_id) REFERENCES public.agent_profiles(user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: customer_profiles customer_profiles_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_profiles
    ADD CONSTRAINT customer_profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: distribution_batches distribution_batches_agent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.distribution_batches
    ADD CONSTRAINT distribution_batches_agent_id_fkey FOREIGN KEY (agent_id) REFERENCES public.agent_profiles(user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: distribution_batches distribution_batches_currency_code_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.distribution_batches
    ADD CONSTRAINT distribution_batches_currency_code_fkey FOREIGN KEY (currency_code) REFERENCES public.currencies(code) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: distribution_batches distribution_batches_maker_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.distribution_batches
    ADD CONSTRAINT distribution_batches_maker_id_fkey FOREIGN KEY (maker_id) REFERENCES public.maker_profiles(user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: distribution_batches distribution_batches_product_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.distribution_batches
    ADD CONSTRAINT distribution_batches_product_id_fkey FOREIGN KEY (product_id) REFERENCES public.products(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: maker_profiles maker_profiles_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.maker_profiles
    ADD CONSTRAINT maker_profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: manual_journals manual_journals_currency_code_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.manual_journals
    ADD CONSTRAINT manual_journals_currency_code_fkey FOREIGN KEY (currency_code) REFERENCES public.currencies(code) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: manual_journals manual_journals_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.manual_journals
    ADD CONSTRAINT manual_journals_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer_profiles(user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: manual_journals manual_journals_maker_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.manual_journals
    ADD CONSTRAINT manual_journals_maker_id_fkey FOREIGN KEY (maker_id) REFERENCES public.maker_profiles(user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: miner_daily_earnings miner_daily_earnings_miner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.miner_daily_earnings
    ADD CONSTRAINT miner_daily_earnings_miner_id_fkey FOREIGN KEY (miner_id) REFERENCES public.miners(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: miner_daily_snapshots miner_daily_snapshots_miner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.miner_daily_snapshots
    ADD CONSTRAINT miner_daily_snapshots_miner_id_fkey FOREIGN KEY (miner_id) REFERENCES public.miners(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: miners miners_facility_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.miners
    ADD CONSTRAINT miners_facility_id_fkey FOREIGN KEY (facility_id) REFERENCES public.facilities(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: order_distributions order_distributions_batch_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_distributions
    ADD CONSTRAINT order_distributions_batch_id_fkey FOREIGN KEY (batch_id) REFERENCES public.distribution_batches(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: order_distributions order_distributions_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_distributions
    ADD CONSTRAINT order_distributions_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer_profiles(user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: order_distributions order_distributions_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_distributions
    ADD CONSTRAINT order_distributions_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: orders orders_agent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_agent_id_fkey FOREIGN KEY (agent_id) REFERENCES public.agent_profiles(user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: orders orders_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer_profiles(user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: orders orders_product_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_product_id_fkey FOREIGN KEY (product_id) REFERENCES public.products(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: products products_agent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.products
    ADD CONSTRAINT products_agent_id_fkey FOREIGN KEY (agent_id) REFERENCES public.agent_profiles(user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: products products_maker_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.products
    ADD CONSTRAINT products_maker_id_fkey FOREIGN KEY (maker_id) REFERENCES public.maker_profiles(user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: products products_miner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.products
    ADD CONSTRAINT products_miner_id_fkey FOREIGN KEY (miner_id) REFERENCES public.miners(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: products products_technician_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.products
    ADD CONSTRAINT products_technician_id_fkey FOREIGN KEY (technician_id) REFERENCES public.technician_profiles(user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: technician_profiles technician_profiles_maker_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.technician_profiles
    ADD CONSTRAINT technician_profiles_maker_id_fkey FOREIGN KEY (maker_id) REFERENCES public.maker_profiles(user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: technician_profiles technician_profiles_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.technician_profiles
    ADD CONSTRAINT technician_profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: transactions transactions_agent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.transactions
    ADD CONSTRAINT transactions_agent_id_fkey FOREIGN KEY (agent_id) REFERENCES public.agent_profiles(user_id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: transactions transactions_audit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.transactions
    ADD CONSTRAINT transactions_audit_id_fkey FOREIGN KEY (audit_id) REFERENCES public.audit_logs(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: transactions transactions_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.transactions
    ADD CONSTRAINT transactions_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: transactions transactions_receiver_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.transactions
    ADD CONSTRAINT transactions_receiver_user_id_fkey FOREIGN KEY (receiver_user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: transactions transactions_sender_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.transactions
    ADD CONSTRAINT transactions_sender_user_id_fkey FOREIGN KEY (sender_user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: user_assets user_assets_currency_code_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_assets
    ADD CONSTRAINT user_assets_currency_code_fkey FOREIGN KEY (currency_code) REFERENCES public.currencies(code) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: user_assets user_assets_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_assets
    ADD CONSTRAINT user_assets_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: withdrawals withdrawals_currency_code_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.withdrawals
    ADD CONSTRAINT withdrawals_currency_code_fkey FOREIGN KEY (currency_code) REFERENCES public.currencies(code) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: withdrawals withdrawals_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.withdrawals
    ADD CONSTRAINT withdrawals_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: agent_profiles agent_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY agent_del_admin ON public.agent_profiles FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: agent_profiles agent_ins_owner_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY agent_ins_owner_admin ON public.agent_profiles FOR INSERT WITH CHECK (((user_id = auth.uid()) OR (public.current_user_role() = 'maker'::text)));


--
-- Name: agent_profiles; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.agent_profiles ENABLE ROW LEVEL SECURITY;

--
-- Name: agent_profiles agent_sel_owner_maker_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY agent_sel_owner_maker_admin ON public.agent_profiles FOR SELECT USING (true);


--
-- Name: agent_profiles agent_upd_owner_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY agent_upd_owner_admin ON public.agent_profiles FOR UPDATE USING (((user_id = auth.uid()) OR (public.current_user_role() = 'maker'::text))) WITH CHECK (((user_id = auth.uid()) OR (public.current_user_role() = 'maker'::text)));


--
-- Name: user_assets assets_ins_owner_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY assets_ins_owner_admin ON public.user_assets FOR INSERT WITH CHECK (((user_id = auth.uid()) OR (public.current_user_role() = ANY (ARRAY['maker'::text, 'agent'::text]))));


--
-- Name: user_assets assets_read_owner_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY assets_read_owner_admin ON public.user_assets FOR SELECT USING (true);


--
-- Name: user_assets assets_upd_owner_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY assets_upd_owner_admin ON public.user_assets FOR UPDATE USING (((user_id = auth.uid()) OR (public.current_user_role() = ANY (ARRAY['maker'::text, 'agent'::text])))) WITH CHECK (((user_id = auth.uid()) OR (public.current_user_role() = ANY (ARRAY['maker'::text, 'agent'::text]))));


--
-- Name: audit_logs audit_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY audit_del_admin ON public.audit_logs FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: audit_logs audit_ins_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY audit_ins_admin ON public.audit_logs FOR INSERT WITH CHECK ((public.current_user_role() = ANY (ARRAY['maker'::text, 'agent'::text])));


--
-- Name: audit_logs; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

--
-- Name: audit_logs audit_read_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY audit_read_admin ON public.audit_logs FOR SELECT USING ((public.current_user_role() = ANY (ARRAY['maker'::text, 'agent'::text, 'technician'::text])));


--
-- Name: audit_logs audit_upd_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY audit_upd_admin ON public.audit_logs FOR UPDATE USING ((public.current_user_role() = 'maker'::text)) WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: distribution_batches batch_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY batch_del_admin ON public.distribution_batches FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: distribution_batches batch_ins_maker_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY batch_ins_maker_admin ON public.distribution_batches FOR INSERT WITH CHECK (((maker_id = auth.uid()) OR (public.current_user_role() = 'maker'::text)));


--
-- Name: distribution_batches batch_read_maker_agent_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY batch_read_maker_agent_admin ON public.distribution_batches FOR SELECT USING (((maker_id = auth.uid()) OR (agent_id = auth.uid()) OR (public.current_user_role() = ANY (ARRAY['maker'::text, 'technician'::text]))));


--
-- Name: distribution_batches batch_upd_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY batch_upd_admin ON public.distribution_batches FOR UPDATE USING ((public.current_user_role() = 'maker'::text)) WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: capacity_requests; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.capacity_requests ENABLE ROW LEVEL SECURITY;

--
-- Name: currencies cur_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY cur_del_admin ON public.currencies FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: currencies cur_ins_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY cur_ins_admin ON public.currencies FOR INSERT WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: currencies cur_read_all; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY cur_read_all ON public.currencies FOR SELECT USING (true);


--
-- Name: currencies cur_upd_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY cur_upd_admin ON public.currencies FOR UPDATE USING ((public.current_user_role() = 'maker'::text)) WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: currencies; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.currencies ENABLE ROW LEVEL SECURITY;

--
-- Name: customer_profiles cust_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY cust_del_admin ON public.customer_profiles FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: customer_profiles cust_ins_self_agent_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY cust_ins_self_agent_admin ON public.customer_profiles FOR INSERT WITH CHECK (((user_id = auth.uid()) OR (public.current_user_role() = ANY (ARRAY['maker'::text, 'agent'::text]))));


--
-- Name: customer_profiles cust_sel_self_agent_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY cust_sel_self_agent_admin ON public.customer_profiles FOR SELECT USING (((user_id = auth.uid()) OR (public.current_user_role() = ANY (ARRAY['maker'::text, 'agent'::text, 'technician'::text]))));


--
-- Name: customer_profiles cust_upd_self_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY cust_upd_self_admin ON public.customer_profiles FOR UPDATE USING (((user_id = auth.uid()) OR (public.current_user_role() = ANY (ARRAY['maker'::text, 'agent'::text])))) WITH CHECK (((user_id = auth.uid()) OR (public.current_user_role() = ANY (ARRAY['maker'::text, 'agent'::text]))));


--
-- Name: customer_profiles; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.customer_profiles ENABLE ROW LEVEL SECURITY;

--
-- Name: order_distributions dist_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY dist_del_admin ON public.order_distributions FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: order_distributions dist_ins_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY dist_ins_admin ON public.order_distributions FOR INSERT WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: order_distributions dist_read_owner_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY dist_read_owner_admin ON public.order_distributions FOR SELECT USING (((customer_id = auth.uid()) OR (public.current_user_role() = ANY (ARRAY['maker'::text, 'agent'::text, 'technician'::text]))));


--
-- Name: order_distributions dist_upd_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY dist_upd_admin ON public.order_distributions FOR UPDATE USING ((public.current_user_role() = 'maker'::text)) WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: distribution_batches; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.distribution_batches ENABLE ROW LEVEL SECURITY;

--
-- Name: miner_daily_earnings earning_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY earning_del_admin ON public.miner_daily_earnings FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: miner_daily_earnings earning_ins_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY earning_ins_admin ON public.miner_daily_earnings FOR INSERT WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: miner_daily_earnings earning_read_all_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY earning_read_all_admin ON public.miner_daily_earnings FOR SELECT USING (true);


--
-- Name: miner_daily_earnings earning_upd_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY earning_upd_admin ON public.miner_daily_earnings FOR UPDATE USING ((public.current_user_role() = 'maker'::text)) WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: capacity_requests exp_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY exp_del_admin ON public.capacity_requests FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: capacity_requests exp_ins_maker; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY exp_ins_maker ON public.capacity_requests FOR INSERT WITH CHECK ((public.current_user_role() = ANY (ARRAY['maker'::text, 'agent'::text])));


--
-- Name: capacity_requests exp_read_owner_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY exp_read_owner_admin ON public.capacity_requests FOR SELECT USING ((public.current_user_role() = ANY (ARRAY['maker'::text, 'agent'::text])));


--
-- Name: capacity_requests exp_upd_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY exp_upd_admin ON public.capacity_requests FOR UPDATE USING ((public.current_user_role() = 'maker'::text)) WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: facilities fac_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY fac_del_admin ON public.facilities FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: facilities fac_ins_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY fac_ins_admin ON public.facilities FOR INSERT WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: facilities fac_read_all; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY fac_read_all ON public.facilities FOR SELECT USING (true);


--
-- Name: facilities fac_upd_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY fac_upd_admin ON public.facilities FOR UPDATE USING ((public.current_user_role() = 'maker'::text)) WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: facilities; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.facilities ENABLE ROW LEVEL SECURITY;

--
-- Name: maker_profiles maker_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY maker_del_admin ON public.maker_profiles FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: maker_profiles maker_ins_owner_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY maker_ins_owner_admin ON public.maker_profiles FOR INSERT WITH CHECK (((user_id = auth.uid()) OR (public.current_user_role() = 'maker'::text)));


--
-- Name: maker_profiles; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.maker_profiles ENABLE ROW LEVEL SECURITY;

--
-- Name: maker_profiles maker_sel_owner_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY maker_sel_owner_admin ON public.maker_profiles FOR SELECT USING (true);


--
-- Name: maker_profiles maker_upd_owner_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY maker_upd_owner_admin ON public.maker_profiles FOR UPDATE USING (((user_id = auth.uid()) OR (public.current_user_role() = 'maker'::text))) WITH CHECK (((user_id = auth.uid()) OR (public.current_user_role() = 'maker'::text)));


--
-- Name: manual_journals; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.manual_journals ENABLE ROW LEVEL SECURITY;

--
-- Name: miner_daily_earnings; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.miner_daily_earnings ENABLE ROW LEVEL SECURITY;

--
-- Name: miner_daily_snapshots; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.miner_daily_snapshots ENABLE ROW LEVEL SECURITY;

--
-- Name: miners; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.miners ENABLE ROW LEVEL SECURITY;

--
-- Name: miners miners_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY miners_del_admin ON public.miners FOR DELETE USING ((public.current_user_role() = ANY (ARRAY['maker'::text, 'admin'::text])));


--
-- Name: miners miners_ins_maker_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY miners_ins_maker_admin ON public.miners FOR INSERT WITH CHECK ((public.current_user_role() = ANY (ARRAY['maker'::text, 'admin'::text])));


--
-- Name: miners miners_read_all; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY miners_read_all ON public.miners FOR SELECT USING (true);


--
-- Name: miners miners_upd_maker_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY miners_upd_maker_admin ON public.miners FOR UPDATE USING ((public.current_user_role() = ANY (ARRAY['maker'::text, 'admin'::text]))) WITH CHECK ((public.current_user_role() = ANY (ARRAY['maker'::text, 'admin'::text])));


--
-- Name: manual_journals mj_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY mj_del_admin ON public.manual_journals FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: manual_journals mj_ins_maker_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY mj_ins_maker_admin ON public.manual_journals FOR INSERT WITH CHECK (((maker_id = auth.uid()) OR (public.current_user_role() = 'maker'::text)));


--
-- Name: manual_journals mj_read_owner_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY mj_read_owner_admin ON public.manual_journals FOR SELECT USING (((maker_id = auth.uid()) OR (customer_id = auth.uid()) OR (public.current_user_role() = ANY (ARRAY['maker'::text, 'technician'::text]))));


--
-- Name: manual_journals mj_upd_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY mj_upd_admin ON public.manual_journals FOR UPDATE USING ((public.current_user_role() = 'maker'::text)) WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: network_stats net_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY net_del_admin ON public.network_stats FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: network_stats net_ins_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY net_ins_admin ON public.network_stats FOR INSERT WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: network_stats net_read_all; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY net_read_all ON public.network_stats FOR SELECT USING (true);


--
-- Name: network_stats net_upd_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY net_upd_admin ON public.network_stats FOR UPDATE USING ((public.current_user_role() = 'maker'::text)) WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: network_stats; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.network_stats ENABLE ROW LEVEL SECURITY;

--
-- Name: orders order_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY order_del_admin ON public.orders FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: order_distributions; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.order_distributions ENABLE ROW LEVEL SECURITY;

--
-- Name: orders order_ins_owner_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY order_ins_owner_admin ON public.orders FOR INSERT WITH CHECK (((customer_id = auth.uid()) OR (agent_id = auth.uid()) OR (public.current_user_role() = 'maker'::text)));


--
-- Name: orders order_read_owner_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY order_read_owner_admin ON public.orders FOR SELECT USING (((customer_id = auth.uid()) OR (agent_id = auth.uid()) OR (public.current_user_role() = ANY (ARRAY['maker'::text, 'technician'::text]))));


--
-- Name: orders order_upd_owner_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY order_upd_owner_admin ON public.orders FOR UPDATE USING (((customer_id = auth.uid()) OR (agent_id = auth.uid()) OR (public.current_user_role() = 'maker'::text))) WITH CHECK (((customer_id = auth.uid()) OR (agent_id = auth.uid()) OR (public.current_user_role() = 'maker'::text)));


--
-- Name: orders; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;

--
-- Name: products prod_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY prod_del_admin ON public.products FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: products prod_ins_maker_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY prod_ins_maker_admin ON public.products FOR INSERT WITH CHECK ((public.current_user_role() = ANY (ARRAY['maker'::text, 'agent'::text])));


--
-- Name: products prod_read_all; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY prod_read_all ON public.products FOR SELECT USING (true);


--
-- Name: products prod_upd_maker_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY prod_upd_maker_admin ON public.products FOR UPDATE USING (((maker_id = auth.uid()) OR (public.current_user_role() = 'maker'::text))) WITH CHECK (((maker_id = auth.uid()) OR (public.current_user_role() = 'maker'::text)));


--
-- Name: products; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;

--
-- Name: miner_daily_snapshots snapshot_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY snapshot_del_admin ON public.miner_daily_snapshots FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: miner_daily_snapshots snapshot_ins_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY snapshot_ins_admin ON public.miner_daily_snapshots FOR INSERT WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: miner_daily_snapshots snapshot_read_all_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY snapshot_read_all_admin ON public.miner_daily_snapshots FOR SELECT USING (true);


--
-- Name: miner_daily_snapshots snapshot_upd_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY snapshot_upd_admin ON public.miner_daily_snapshots FOR UPDATE USING ((public.current_user_role() = 'maker'::text)) WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: technician_profiles tech_sel_owner_maker_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tech_sel_owner_maker_admin ON public.technician_profiles FOR SELECT USING (((public.current_user_role() = 'agent'::text) OR (maker_id = auth.uid()) OR (public.current_user_role() = 'maker'::text)));


--
-- Name: technician_profiles; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.technician_profiles ENABLE ROW LEVEL SECURITY;

--
-- Name: transactions; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;

--
-- Name: transactions tx_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tx_del_admin ON public.transactions FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: transactions tx_ins_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tx_ins_admin ON public.transactions FOR INSERT WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: transactions tx_read_party_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tx_read_party_admin ON public.transactions FOR SELECT USING (((sender_user_id = auth.uid()) OR (public.current_user_role() = ANY (ARRAY['maker'::text, 'agent'::text, 'technician'::text]))));


--
-- Name: transactions tx_upd_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tx_upd_admin ON public.transactions FOR UPDATE USING ((public.current_user_role() = 'maker'::text)) WITH CHECK ((public.current_user_role() = 'maker'::text));


--
-- Name: user_assets; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.user_assets ENABLE ROW LEVEL SECURITY;

--
-- Name: users; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

--
-- Name: users users_delete_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY users_delete_admin ON public.users FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: users users_insert_self_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY users_insert_self_admin ON public.users FOR INSERT WITH CHECK (((id = auth.uid()) OR (public.current_user_role() = 'maker'::text)));


--
-- Name: users users_select_own; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY users_select_own ON public.users FOR SELECT USING ((auth.role() = 'authenticated'::text));


--
-- Name: users users_update_self_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY users_update_self_admin ON public.users FOR UPDATE USING (((id = auth.uid()) OR (public.current_user_role() = 'maker'::text))) WITH CHECK (((id = auth.uid()) OR (public.current_user_role() = 'maker'::text)));


--
-- Name: withdrawals wd_del_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY wd_del_admin ON public.withdrawals FOR DELETE USING ((public.current_user_role() = 'maker'::text));


--
-- Name: withdrawals wd_ins_owner; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY wd_ins_owner ON public.withdrawals FOR INSERT WITH CHECK ((user_id = auth.uid()));


--
-- Name: withdrawals wd_read_owner_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY wd_read_owner_admin ON public.withdrawals FOR SELECT USING (((user_id = auth.uid()) OR (public.current_user_role() = ANY (ARRAY['maker'::text, 'agent'::text]))));


--
-- Name: withdrawals wd_upd_admin; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY wd_upd_admin ON public.withdrawals FOR UPDATE USING ((public.current_user_role() = ANY (ARRAY['maker'::text, 'agent'::text]))) WITH CHECK ((public.current_user_role() = ANY (ARRAY['maker'::text, 'agent'::text])));


--
-- Name: withdrawals; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.withdrawals ENABLE ROW LEVEL SECURITY;

--
-- PostgreSQL database dump complete
--

