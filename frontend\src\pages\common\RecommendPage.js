import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Alert, Pagination } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import { QRCodeSVG } from 'qrcode.react';
import { FaCopy } from 'react-icons/fa';

const RecommendPage = () => {
    const { t } = useTranslation();
    const [referrals, setReferrals] = useState([]);
    const [loading, setLoading] = useState(true);
    const [inviteCode, setInviteCode] = useState('');
    const [registrationUrl, setRegistrationUrl] = useState('');
    const [copySuccess, setCopySuccess] = useState('');
    const [error, setError] = useState('');

    // Pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [referralsPerPage] = useState(10);
    const [paginatedReferrals, setPaginatedReferrals] = useState([]);

    useEffect(() => {
        const fetchReferrals = async () => {
            const supabase = getSupabase();
            if (!supabase) {
                setError(t('backend_connection_failed'));
                setLoading(false);
                return;
            }

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                setError(t('not_logged_in'));
                return;
            }

            // Fetch current user's invite code
            const { data: userData, error: userError } = await supabase
                .from('users')
                .select('invite_code')
                .eq('id', user.id)
                .single();

            if (userError) {
                console.error('Error fetching invite code:', userError);
                setError(t('failed_to_load_invite_code'));
            } else if (userData) {
                setInviteCode(userData.invite_code);
                // Create registration URL with invite code
                const baseUrl = window.location.origin + window.location.pathname;
                const registrationPath = `#/register/${userData.invite_code}`;
                setRegistrationUrl(baseUrl + registrationPath);
            }

            // Fetch users referred by current user
            const { data, error } = await supabase
                .from('users')
                .select('id, email, created_at')
                .eq('referred_by', user.id)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching referrals:', error);
                setError(t('failed_to_load_referral_data'));
            } else {
                setReferrals(data || []);
            }
            setLoading(false);
        };

        fetchReferrals();
    }, [t]);

    // Paginate referrals
    useEffect(() => {
        const indexOfLastReferral = currentPage * referralsPerPage;
        const indexOfFirstReferral = indexOfLastReferral - referralsPerPage;
        const currentReferrals = referrals.slice(indexOfFirstReferral, indexOfLastReferral);
        setPaginatedReferrals(currentReferrals);
    }, [referrals, currentPage, referralsPerPage]);

    // Pagination handlers
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const totalPages = Math.ceil(referrals.length / referralsPerPage);

    const copyToClipboard = (text, type) => {
        navigator.clipboard.writeText(text);
        setCopySuccess(type === 'code' ? t('invite_code_copied') : t('registration_link_copied'));
        setTimeout(() => setCopySuccess(''), 3000);
    };

    if (loading) {
        return <Container><div className="text-center my-5">{t('loading_referral_data')}</div></Container>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('my_recommendations')}</h2>

            {error && <Alert variant="danger">{error}</Alert>}
            {copySuccess && <Alert variant="success">{copySuccess}</Alert>}

            <Row className="mb-4">
                <Col md={6}>
                    <Card>
                        <Card.Body>
                            <Card.Title>{t('my_invite_code')}</Card.Title>
                            <div className="d-flex align-items-center mb-3">
                                <p className="lead mb-0 me-3"><strong>{inviteCode || 'N/A'}</strong></p>
                                {inviteCode && (
                                    <Button
                                        variant="outline-primary"
                                        size="sm"
                                        onClick={() => copyToClipboard(inviteCode, 'code')}
                                    >
                                        <FaCopy className="me-1" /> {t('copy_invite_code')}
                                    </Button>
                                )}
                            </div>

                            {inviteCode && (
                                <>
                                    <div className="d-flex align-items-center mb-3">
                                        <p className="text-break mb-0 me-3">{registrationUrl}</p>
                                        <Button
                                            variant="outline-primary"
                                            size="sm"
                                            onClick={() => copyToClipboard(registrationUrl, 'link')}
                                        >
                                            <FaCopy className="me-1" /> {t('copy_link')}
                                        </Button>
                                    </div>
                                    <p className="mt-3 text-muted">{t('share_invite_description')}</p>
                                </>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6}>
                    <Card>
                        <Card.Body className="text-center">
                            <Card.Title>{t('registration_qr_code')}</Card.Title>
                            {inviteCode ? (
                                <div className="my-3">
                                    <QRCodeSVG
                                        value={registrationUrl}
                                        size={200}
                                        level="H"
                                        includeMargin={true}
                                    />
                                    <p className="mt-3 text-muted">{t('scan_qr_code_to_register')}</p>
                                </div>
                            ) : (
                                <p>{t('no_invite_code_available')}</p>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Card.Title>{t('my_subordinate_users')}</Card.Title>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('user_id')}</th>
                                        <th>{t('email')}</th>
                                        <th>{t('registration_time')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedReferrals.length === 0 ? (
                                        <tr>
                                            <td colSpan="3" className="text-center">{t('no_subordinate_users')}</td>
                                        </tr>
                                    ) : (
                                        paginatedReferrals.map(ref => (
                                            <tr key={ref.id}>
                                                <td>{ref.id.substring(0, 8)}...</td>
                                                <td>{ref.email}</td>
                                                <td>{new Date(ref.created_at).toLocaleString()}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="d-flex justify-content-center">
                                    <Pagination>
                                        <Pagination.First
                                            onClick={() => handlePageChange(1)}
                                            disabled={currentPage === 1}
                                        />
                                        <Pagination.Prev
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                        />

                                        {/* Show page numbers */}
                                        {[...Array(totalPages)].map((_, index) => {
                                            const pageNumber = index + 1;
                                            // Show first page, last page, current page, and pages around current page
                                            if (
                                                pageNumber === 1 ||
                                                pageNumber === totalPages ||
                                                (pageNumber >= currentPage - 2 && pageNumber <= currentPage + 2)
                                            ) {
                                                return (
                                                    <Pagination.Item
                                                        key={pageNumber}
                                                        active={pageNumber === currentPage}
                                                        onClick={() => handlePageChange(pageNumber)}
                                                    >
                                                        {pageNumber}
                                                    </Pagination.Item>
                                                );
                                            } else if (
                                                pageNumber === currentPage - 3 ||
                                                pageNumber === currentPage + 3
                                            ) {
                                                return <Pagination.Ellipsis key={pageNumber} />;
                                            }
                                            return null;
                                        })}

                                        <Pagination.Next
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                        />
                                        <Pagination.Last
                                            onClick={() => handlePageChange(totalPages)}
                                            disabled={currentPage === totalPages}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default RecommendPage;
