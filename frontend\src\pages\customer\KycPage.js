
import React, { useState, useEffect } from 'react';
import { Container, Form, Button, Card, Alert } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const KycPage = () => {
    const { t } = useTranslation();
    const [realName, setRealName] = useState('');
    const [idNumber, setIdNumber] = useState('');
    const [idImgFront, setIdImgFront] = useState(null);
    const [idImgBack, setIdImgBack] = useState(null);
    const [verifyStatus, setVerifyStatus] = useState(null); // null, 'pending', 'approved', 'rejected'
    const [loading, setLoading] = useState(true);
    const [submitting, setSubmitting] = useState(false);
    const [message, setMessage] = useState({ type: '', text: '' });

    useEffect(() => {
        const fetchKycStatus = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                setLoading(false);
                return;
            }

            const { data, error } = await supabase
                .from('customer_profiles')
                .select('real_name, id_number, id_img_front, id_img_back, verify_status')
                .eq('user_id', user.id)
                .single();

            if (error && error.code !== 'PGRST116') { // PGRST116 means no rows found
                console.error('Error fetching KYC status:', error);
                setMessage({ type: 'danger', text: t('failed_to_load_kyc_status') });
            } else if (data) {
                setRealName(data.real_name || '');
                setIdNumber(data.id_number || '');
                setIdImgFront(data.id_img_front || null);
                setIdImgBack(data.id_img_back || null);
                setVerifyStatus(data.verify_status || null);
            }
            setLoading(false);
        };

        fetchKycStatus();
    }, []);

    const handleFileChange = (e, setImage) => {
        if (e.target.files && e.target.files[0]) {
            setImage(e.target.files[0]);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSubmitting(true);
        setMessage({ type: '', text: '' });

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Supabase not initialized');
            }

            // Get current user ID
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                throw new Error('User not authenticated');
            }

            const formData = new FormData();
            formData.append('real_name', realName);
            formData.append('id_number', idNumber);
            formData.append('user_id', user.id); // Add user ID

            // Add files if they are new File objects
            if (idImgFront instanceof File) {
                formData.append('id_img_front', idImgFront);
            }

            if (idImgBack instanceof File) {
                formData.append('id_img_back', idImgBack);
            }

            const response = await fetch(`${window.wpData.apiUrl}submit-kyc`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-WP-Nonce': window.wpData.nonce
                }
            });
            

            const result = await response.json();

            if (!result.success) {
                throw new Error(t(result.error_code) || 'Failed to submit KYC');
            }

            // Update local state with new image URLs if uploaded
            if (result.files && result.files.id_img_front) {
                setIdImgFront(result.files.id_img_front.url);
            }

            if (result.files && result.files.id_img_back) {
                setIdImgBack(result.files.id_img_back.url);
            }

            setMessage({ type: 'success', text: t('kyc_submit_success') });
            setVerifyStatus('pending');

            // Show any upload warnings if present
            if (result.errors && result.errors.length > 0) {
                console.warn('Upload warnings:', result.errors);
            }

        } catch (error) {
            console.error('KYC submission error:', error);
            setMessage({ type: 'danger', text: t('failed_to_submit_kyc') + ': ' + error.message });
        }

        setSubmitting(false);
    };

    if (loading) {
        return <div>{t('loading_kyc_status')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('kyc_verification')}</h2>
            <Card>
                <Card.Body>
                    {message.text && <Alert variant={message.type}>{message.text}</Alert>}
                    
                    {verifyStatus === 'approved' && (
                        <Alert variant="success">{t('kyc_approved')}</Alert>
                    )}
                    {verifyStatus === 'pending' && (
                        <Alert variant="info">{t('kyc_pending_review')}</Alert>
                    )}
                    {verifyStatus === 'rejected' && (
                        <Alert variant="danger">{t('kyc_rejected')}</Alert>
                    )}
                    {verifyStatus === null && (
                        <Alert variant="warning">{t('kyc_not_submitted')}</Alert>
                    )}

                    <Form onSubmit={handleSubmit}>
                        <Form.Group className="mb-3">
                            <Form.Label><span className="text-danger">*</span>{t('real_name')}</Form.Label>
                            <Form.Control 
                                type="text" 
                                value={realName} 
                                onChange={(e) => setRealName(e.target.value)} 
                                required 
                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}
                            />
                        </Form.Group>
                        <Form.Group className="mb-3">
                            <Form.Label><span className="text-danger">*</span>{t('id_number')}</Form.Label>
                            <Form.Control 
                                type="text" 
                                value={idNumber} 
                                onChange={(e) => setIdNumber(e.target.value)} 
                                required 
                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}
                            />
                        </Form.Group>
                        <Form.Group className="mb-3">
                            <Form.Label>{t('id_front')}</Form.Label>
                            <Form.Control 
                                type="file" 
                                onChange={(e) => handleFileChange(e, setIdImgFront)} 
                                accept="image/*" 
                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}
                            />
                            {idImgFront && typeof idImgFront === 'string' && (
                                <img src={idImgFront} alt="ID Front" className="img-thumbnail mt-2" style={{ maxWidth: '200px' }} />
                            )}
                        </Form.Group>
                        <Form.Group className="mb-3">
                            <Form.Label>{t('id_back')}</Form.Label>
                            <Form.Control 
                                type="file" 
                                onChange={(e) => handleFileChange(e, setIdImgBack)} 
                                accept="image/*" 
                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}
                            />
                            {idImgBack && typeof idImgBack === 'string' && (
                                <img src={idImgBack} alt="ID Back" className="img-thumbnail mt-2" style={{ maxWidth: '200px' }} />
                            )}
                        </Form.Group>
                        
                        <Button 
                            variant="primary" 
                            type="submit" 
                            disabled={submitting || verifyStatus === 'pending' || verifyStatus === 'approved'}
                        >
                            {submitting ? t('submitting') : t('submit_review')}
                        </Button>
                    </Form>
                </Card.Body>
            </Card>
        </Container>
    );
};

export default KycPage;
