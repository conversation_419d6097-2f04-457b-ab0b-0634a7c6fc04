import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, Button, Modal, Form, Alert } from 'react-bootstrap';
import { FaTimes, FaDownload, FaPlus } from 'react-icons/fa';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import StatusBadge from '../../components/StatusBadge';

const ManualDeposits = () => {
    const { t } = useTranslation();
    const [journals, setJournals] = useState([]);
    const [loading, setLoading] = useState(true);

    // Add Manual Deposit Modal states
    const [showAddModal, setShowAddModal] = useState(false);
    const [addLoading, setAddLoading] = useState(false);
    const [addError, setAddError] = useState('');
    const [addSuccess, setAddSuccess] = useState('');
    const [customers, setCustomers] = useState([]);
    const [currencies, setCurrencies] = useState([]);

    // Form data
    const [depositForm, setDepositForm] = useState({
        customer_id: '',
        currency_code: 'FIL',
        amount: '',
        journal_type: 'deposit',
        remark: ''
    });

    useEffect(() => {
        const fetchData = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch manual journals with maker, customer and currency information
            const { data: journalsData, error: journalsError } = await supabase
                .from('manual_journals')
                .select(`
                    id,
                    amount,
                    journal_type,
                    remark,
                    created_at,
                    maker_profiles (
                        domain,
                        users (
                            email
                        )
                    ),
                    customer_profiles (
                        real_name,
                        users (
                            email
                        )
                    ),
                    currencies (
                        code,
                        total_supply,
                        withdrawable
                    )
                `)
                .order('created_at', { ascending: false });

            if (journalsError) {
                console.error('Error fetching manual journals:', journalsError);
            } else {
                setJournals(journalsData);
            }

            // Fetch customers for dropdown
            const { data: customersData, error: customersError } = await supabase
                .from('customer_profiles')
                .select(`
                    user_id,
                    real_name,
                    users (
                        email
                    )
                `)
                .order('created_at', { ascending: false });

            if (customersError) {
                console.error('Error fetching customers:', customersError);
            } else {
                setCustomers(customersData || []);
            }

            // Fetch currencies for dropdown
            const { data: currenciesData, error: currenciesError } = await supabase
                .from('currencies')
                .select('code, total_supply, withdrawable')
                .order('code');

            if (currenciesError) {
                console.error('Error fetching currencies:', currenciesError);
            } else {
                setCurrencies(currenciesData || []);
            }

            setLoading(false);
        };

        fetchData();
    }, []);

    // Handle Add Manual Deposit
    const handleAddDeposit = () => {
        setShowAddModal(true);
        setDepositForm({
            customer_id: '',
            currency_code: 'FIL',
            amount: '',
            journal_type: 'deposit',
            remark: ''
        });
        setAddError('');
        setAddSuccess('');
    };

    const handleFormChange = (field, value) => {
        setDepositForm(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const validateForm = () => {
        if (!depositForm.customer_id) {
            setAddError(t('customer_selection_required'));
            return false;
        }
        if (!depositForm.currency_code) {
            setAddError(t('currency_selection_required'));
            return false;
        }
        if (!depositForm.amount || parseFloat(depositForm.amount) === 0) {
            setAddError(t('amount_required'));
            return false;
        }
        if (!depositForm.journal_type) {
            setAddError(t('journal_type_required'));
            return false;
        }
        return true;
    };

    const handleConfirmAddDeposit = async () => {
        if (!validateForm()) {
            return;
        }

        setAddLoading(true);
        setAddError('');
        setAddSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Get current user (maker)
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                throw new Error('Maker not authenticated');
            }

            const amount = parseFloat(depositForm.amount);

            // Get customer's agent_id for transaction record
            const { data: customerProfile, error: customerError } = await supabase
                .from('customer_profiles')
                .select('agent_id')
                .eq('user_id', depositForm.customer_id)
                .single();

            if (customerError) {
                console.error('Error fetching customer profile:', customerError);
                // Continue without agent_id if customer profile not found
            }

            const customerAgentId = customerProfile?.agent_id || null;

            // Generate UUID for manual journal
            const journalId = crypto.randomUUID();

            // Start transaction-like operations
            // 1. Insert manual journal record
            const { error: journalError } = await supabase
                .from('manual_journals')
                .insert({
                    id: journalId,
                    maker_id: user.id,
                    customer_id: depositForm.customer_id,
                    currency_code: depositForm.currency_code,
                    amount: amount,
                    journal_type: depositForm.journal_type,
                    remark: depositForm.remark || null
                });

            if (journalError) {
                throw journalError;
            }

            // 2. Get current user assets
            const { data: currentAssets, error: assetsError } = await supabase
                .from('user_assets')
                .select('balance_available, balance_total')
                .eq('user_id', depositForm.customer_id)
                .eq('currency_code', depositForm.currency_code)
                .single();

            if (assetsError && assetsError.code !== 'PGRST116') { // PGRST116 = no rows found
                throw assetsError;
            }

            const oldBalanceAvailable = currentAssets?.balance_available || 0;
            const oldBalanceTotal = currentAssets?.balance_total || 0;
            const newBalanceAvailable = oldBalanceAvailable + amount;
            const newBalanceTotal = oldBalanceTotal + amount;

            // 3. Update or insert user assets
            const { error: updateAssetsError } = await supabase
                .from('user_assets')
                .upsert({
                    user_id: depositForm.customer_id,
                    currency_code: depositForm.currency_code,
                    balance_available: newBalanceAvailable,
                    balance_locked: currentAssets?.balance_locked || 0,
                    balance_total: newBalanceTotal,
                    withdrawn_total: currentAssets?.withdrawn_total || 0
                });

            if (updateAssetsError) {
                throw updateAssetsError;
            }

            // 4. Create audit log
            const auditLogId = crypto.randomUUID();
            const { error: auditError } = await supabase
                .from('audit_logs')
                .insert({
                    id: auditLogId,
                    user_id: user.id,
                    action: 'manual_deposit',
                    object_table: 'user_assets',
                    object_id: depositForm.customer_id,
                    diff: {
                        old: {
                            user_assets: {
                                balance_available: oldBalanceAvailable,
                                balance_total: oldBalanceTotal
                            }
                        },
                        new: {
                            user_assets: {
                                balance_available: newBalanceAvailable,
                                balance_total: newBalanceTotal
                            }
                        }
                    }
                });

            if (auditError) {
                throw auditError;
            }

            // 5. Create transaction record
            const { error: transactionError } = await supabase
                .from('transactions')
                .insert({
                    id: journalId,
                    sender_user_id: null, // Manual deposit has no sender
                    receiver_user_id: depositForm.customer_id,
                    amount_net: amount,
                    tx_type: 'manual_deposit',
                    filecoin_msg_id: `manual_${journalId}`,
                    agent_id: customerAgentId, // Use customer's agent_id if available
                    audit_id: auditLogId
                });

            if (transactionError) {
                throw transactionError;
            }

            setAddSuccess(t('manual_deposit_created_successfully'));

            // Refresh journals list
            const { data: updatedJournals, error: refreshError } = await supabase
                .from('manual_journals')
                .select(`
                    id,
                    amount,
                    journal_type,
                    remark,
                    created_at,
                    maker_profiles (
                        domain,
                        users (
                            email
                        )
                    ),
                    customer_profiles (
                        real_name,
                        users (
                            email
                        )
                    ),
                    currencies (
                        code,
                        total_supply,
                        withdrawable
                    )
                `)
                .order('created_at', { ascending: false });

            if (!refreshError) {
                setJournals(updatedJournals);
            }

            // Close modal after 2 seconds
            setTimeout(() => {
                setShowAddModal(false);
            }, 2000);

        } catch (error) {
            console.error('Error creating manual deposit:', error);
            setAddError(error.message || t('manual_deposit_creation_error'));
        } finally {
            setAddLoading(false);
        }
    };

    const closeAddModal = () => {
        setShowAddModal(false);
        setDepositForm({
            customer_id: '',
            currency_code: 'FIL',
            amount: '',
            journal_type: 'deposit',
            remark: ''
        });
        setAddError('');
        setAddSuccess('');
    };

    const formatAmount = (amount) => {
        if (!amount) return '0.000000';
        return parseFloat(amount).toFixed(6);
    };

    // Export journals to CSV
    const exportToCSV = () => {
        if (journals.length === 0) {
            alert(t('no_data_to_export'));
            return;
        }

        // Define CSV headers
        const headers = [
            t('journal_id'),
            t('maker'),
            t('customer'),
            t('currency_code'),
            t('amount'),
            t('journal_type'),
            t('remark'),
            t('created_at')
        ];

        // Convert data to CSV format
        const csvData = journals.map(journal => [
            journal.id,
            journal.maker_profiles?.domain || '-',
            journal.customer_profiles?.real_name || journal.customer_profiles?.users?.email || '-',
            journal.currencies?.code || '-',
            formatAmount(journal.amount),
            t(journal.journal_type) || 'deposit',
            journal.remark || '-',
            new Date(journal.created_at).toLocaleString()
        ]);

        // Combine headers and data
        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Add UTF-8 BOM to ensure proper encoding for Japanese characters
        const BOM = '\uFEFF';
        const csvWithBOM = BOM + csvContent;

        // Create and download CSV file with proper UTF-8 encoding
        const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `manual_deposits_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    if (loading) {
        return <div>{t('loading_manual_deposits')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('manual_deposit')}</h2>

            {/* Top Operation Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('export_data')}</Form.Label>
                                        <div>
                                            <Button
                                                variant="success"
                                                onClick={exportToCSV}
                                                className="mb-2 me-2"
                                                disabled={journals.length === 0}
                                            >
                                                <FaDownload className="me-1" />
                                                {t('export_all')}
                                            </Button>
                                            <Button
                                                variant="primary"
                                                onClick={handleAddDeposit}
                                                className="mb-2"
                                            >
                                                <FaPlus className="me-1" />
                                                {t('add_new_deposit')}
                                            </Button>
                                        </div>
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('journal_id')}</th>
                                        <th>{t('maker')}</th>
                                        <th>{t('customer')}</th>
                                        <th>{t('currency_code')}</th>
                                        <th>{t('amount')}</th>
                                        <th>{t('journal_type')}</th>
                                        <th>{t('remark')}</th>
                                        <th>{t('created_at')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {journals.length === 0 ? (
                                        <tr>
                                            <td colSpan="8" className="text-center">{t('no_manual_deposits_available')}</td>
                                        </tr>
                                    ) : (
                                        journals.map(journal => (
                                            <tr key={journal.id}>
                                                <td className='max-width-150'>{journal.id}</td>
                                                <td>
                                                    <div>
                                                        <div>{journal.maker_profiles?.domain || '-'}</div>
                                                        <small className="text-muted">
                                                            {journal.maker_profiles?.users?.email || '-'}
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <div>{journal.customer_profiles?.real_name || '-'}</div>
                                                        <small className="text-muted">
                                                            {journal.customer_profiles?.users?.email || '-'}
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <Badge bg="primary">
                                                        {journal.currencies?.code || '-'}
                                                    </Badge>
                                                </td>
                                                <td className={journal.amount >= 0 ? 'text-success' : 'text-danger'}>
                                                    {journal.amount >= 0 ? '+' : ''}{formatAmount(journal.amount)}
                                                </td>
                                                <td><StatusBadge status={journal.journal_type} type="journal" /></td>
                                                <td>
                                                    <div style={{ maxWidth: '200px', wordWrap: 'break-word' }}>
                                                        {journal.remark || '-'}
                                                    </div>
                                                </td>
                                                <td>{new Date(journal.created_at).toLocaleString()}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Add Manual Deposit Modal */}
            <Modal show={showAddModal} onHide={closeAddModal} size="lg">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('add_new_deposit')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeAddModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {addError && (
                        <Alert variant="danger" className="mb-3">
                            {addError}
                        </Alert>
                    )}
                    {addSuccess && (
                        <Alert variant="success" className="mb-3">
                            {addSuccess}
                        </Alert>
                    )}

                    <Form>
                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('select_customer')}</strong></Form.Label>
                                    <Form.Select
                                        value={depositForm.customer_id}
                                        onChange={(e) => handleFormChange('customer_id', e.target.value)}
                                        disabled={addLoading}
                                        required
                                    >
                                        <option value="">{t('please_select_customer')}</option>
                                        {customers.map(customer => (
                                            <option key={customer.user_id} value={customer.user_id}>
                                                {customer.real_name || customer.users?.email} ({customer.users?.email})
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('currency_code')}</strong></Form.Label>
                                    <Form.Select
                                        value={depositForm.currency_code}
                                        onChange={(e) => handleFormChange('currency_code', e.target.value)}
                                        disabled={addLoading}
                                        required
                                    >
                                        {currencies.map(currency => (
                                            <option key={currency.code} value={currency.code}>
                                                {currency.code}
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('amount')}</strong></Form.Label>
                                    <Form.Control
                                        type="number"
                                        value={depositForm.amount}
                                        onChange={(e) => handleFormChange('amount', e.target.value)}
                                        placeholder={t('enter_amount')}
                                        disabled={addLoading}
                                        min="0"
                                        step="0.000001"
                                        required
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label><strong>{t('journal_type')}</strong></Form.Label>
                                    <Form.Select
                                        value={depositForm.journal_type}
                                        onChange={(e) => handleFormChange('journal_type', e.target.value)}
                                        disabled={addLoading}
                                        required
                                    >
                                        <option value="deposit">{t('deposit')}</option>
                                        <option value="adjustment">{t('adjustment')}</option>
                                        <option value="bonus">{t('bonus')}</option>
                                        <option value="penalty">{t('penalty')}</option>
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Form.Group className="mb-3">
                            <Form.Label><strong>{t('remark')}</strong></Form.Label>
                            <Form.Control
                                as="textarea"
                                rows={3}
                                value={depositForm.remark}
                                onChange={(e) => handleFormChange('remark', e.target.value)}
                                placeholder={t('enter_remark')}
                                disabled={addLoading}
                            />
                        </Form.Group>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeAddModal} disabled={addLoading}>
                        {t('cancel')}
                    </Button>
                    <Button variant="primary" onClick={handleConfirmAddDeposit} disabled={addLoading}>
                        {addLoading ? t('processing') : t('confirm_add')}
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default ManualDeposits;
